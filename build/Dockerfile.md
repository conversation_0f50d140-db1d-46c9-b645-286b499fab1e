# Dockerfile 說明文件

## 概述
此 Dockerfile 採用多階段構建（Multi-stage build）模式，用於構建和部署一個基於 Node.js 的 Web 應用程序。整個構建過程分為兩個主要階段：構建階段（builder）和運行階段（runner）。

## 構建架構

### 第一階段：構建階段 (builder)
```dockerfile
FROM node:22-alpine AS builder
```

**基礎鏡像：** `node:22-alpine`
- 使用 Node.js 22 版本的 Alpine Linux 發行版
- Alpine Linux 是一個輕量級的 Linux 發行版，鏡像體積小

**工作流程：**

1. **設置工作目錄**
   ```dockerfile
   WORKDIR /workspace
   ```
   - 設置容器內的工作目錄為 `/workspace`

2. **複製源代碼**
   ```dockerfile
   COPY . .
   ```
   - 將主機當前目錄的所有文件複製到容器的工作目錄

3. **啟用包管理器**
   ```dockerfile
   RUN corepack enable
   ```
   - 啟用 Node.js 的 corepack，用於管理包管理器版本

4. **配置私有 npm 註冊表**
   ```dockerfile
   RUN cp /workspace/.npmrc.docker /root/.npmrc
   ```
   - 複製 npm 配置文件，用於訪問私有的 mayo 組件庫
   - 這是本地測試打包所需的步驟

5. **安裝依賴**
   ```dockerfile
   WORKDIR /workspace/src
   RUN pnpm i --frozen-lockfile
   ```
   - 切換到 `/workspace/src` 目錄
   - 使用 pnpm 安裝依賴，`--frozen-lockfile` 確保使用 lockfile 中的確切版本

6. **構建應用**
   ```dockerfile
   RUN pnpm --filter=web build
   ```
   - 使用 pnpm workspace 功能，僅構建 `web` 應用

### 第二階段：運行階段 (runner)
```dockerfile
FROM nginx:1.28.0-alpine AS runner
```

**基礎鏡像：** `nginx:1.28.0-alpine`
- 使用 Nginx 1.28.0 版本的 Alpine Linux 發行版
- 用於提供靜態文件服務

**部署流程：**

1. **複製構建產物**
   ```dockerfile
   COPY --from=builder /workspace/src/apps/web/dist /usr/share/nginx/html
   ```
   - 從 builder 階段複製構建好的靜態文件到 Nginx 的默認靜態文件目錄

2. **配置 Nginx**
   ```dockerfile
   COPY --from=builder /workspace/build/nginx/nginx.conf /usr/share/nginx/conf/nginx.conf
   ```
   - 複製自定義的 Nginx 配置文件

3. **暴露端口**
   ```dockerfile
   EXPOSE 3000
   ```
   - 聲明容器將監聽 3000 端口

4. **啟動服務**
   ```dockerfile
   CMD ["nginx", "-c", "/usr/share/nginx/conf/nginx.conf", "-g", "daemon off;"]
   ```
   - 啟動 Nginx，使用自定義配置文件，並在前台運行

## 構建和運行

### 構建鏡像
```bash
docker build -f build/Dockerfile -t agent-chat-app .
```

### 運行容器
```bash
docker run -p 3000:3000 agent-chat-app
```

## 注意事項

1. **私有依賴**：構建過程需要 `.npmrc.docker` 文件來訪問私有的 mayo 組件庫
2. **工作區結構**：項目使用 pnpm workspace，構建目標是 `web` 應用
3. **端口配置**：應用通過 Nginx 在 3000 端口提供服務
4. **多階段構建優勢**：最終鏡像只包含運行時需要的文件，不包含 Node.js 構建環境，大大減小了鏡像體積

## 依賴文件

- `.npmrc.docker`：npm 私有註冊表配置
- `build/nginx/nginx.conf`：Nginx 服務器配置
- `src/apps/web/`：Web 應用源代碼目錄

## 構建輸出

構建完成後，將生成一個包含以下內容的運行時鏡像：
- Nginx 服務器
- 構建好的 Web 應用靜態文件
- 自定義的 Nginx 配置 