#!/bin/sh
set -e

echo "🚀 開始選擇運行時配置..."

# 設置默認環境為 tst
ENV=${ENV:-"tst"}

# 驗證環境變數是否有效
case "$ENV" in
  dev|tst|uat|preprd|prod)
    echo "✅ 環境設置為: $ENV"
    ;;
  *)
    echo "❌ 錯誤: 無效的環境變數 ENV=$ENV"
    echo "   支援的環境: dev, tst, uat, preprd, prod"
    echo "   使用默認環境: tst"
    ENV="tst"
    ;;
esac

# 選擇對應的配置文件
CONFIG_FILE="/usr/share/nginx/html/configs/config.${ENV}.js"

if [ -f "$CONFIG_FILE" ]; then
  # 複製對應環境的配置文件到 config.js
  cp "$CONFIG_FILE" /usr/share/nginx/html/config.js
  echo "✅ 已載入 $ENV 環境配置"
  echo "📋 配置文件: $CONFIG_FILE"
else
  echo "❌ 錯誤: 找不到配置文件 $CONFIG_FILE"
  echo "   使用 tst 環境作為備用配置"
  cp "/usr/share/nginx/html/configs/config.tst.js" /usr/share/nginx/html/config.js
fi

echo "🎯 當前環境: $ENV"
echo "📁 配置文件已準備完成"

# 執行原始的命令（通常是啟動Nginx）
exec "$@"
