# /etc/nginx/nginx.conf

user nginx;
worker_processes auto;

events {
    worker_connections 1024;  # 最大连接数
}
http {
    types {
        text/html                             html htm shtml;
        text/css                              css;
        text/xml                              xml;
        image/gif                             gif;
        image/jpeg                            jpeg jpg;
        application/javascript                js;
        application/font-woff                 woff;
        application/font-ttf                  ttf;
        application/json                      json;
        application/xml                       xml;
        application/vnd.ms-fontobject         eot;
        image/svg+xml                         svg;
        video/mp4                             mp4;
        video/x-msvideo                       avi;
        video/quicktime                       mov;
        video/mpeg                            mpeg mpg;
        video/webm                            webm;
    }

    server {
            listen       8080;
            server_name  localhost;
            
            gzip  on;  
            gzip_min_length 1k;
            gzip_comp_level 5; 
            gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
            gzip_disable "MSIE [1-6]\.";
            gzip_vary on;

            # 健康檢查端點
            location /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
            }

            # 靜態資源處理 - 不存在時直接回傳 404
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|tif|dpg|json|mp4|rmvb|rm|wmv|avi|3gp)$ {
                    root /usr/share/nginx/html;
                    expires 1y;
                    add_header Cache-Control "public, max-age=31536000, immutable";
                    add_header Access-Control-Allow-Origin *;

                    # 靜態資源不存在時直接回傳 404，不要 fallback
                    try_files $uri =404;
            }

            # SPA 路由處理 - HTML 請求才 fallback 到 index.html
            location / {
                    root /usr/share/nginx/html;
                    index index.html index.htm;

                    # 只對非靜態資源做 SPA 路由回退
                    try_files $uri $uri/ /index.html;

                    # CORS 設定
                    add_header Access-Control-Allow-Origin *;
            }
    }
}
