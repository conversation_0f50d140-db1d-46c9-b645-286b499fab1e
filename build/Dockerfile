FROM node:22-alpine AS builder
WORKDIR /workspace

# 複製源代碼 (在安裝依賴後)
COPY . .
RUN echo "✅ Step 1: 複製源代碼完成"
RUN corepack enable

# 本機測打包才需要複製Access Token檔案，檔案來源是cp ~/.npmrc .npmrc.docker (安裝mayo私有元件庫時會用到)
RUN cp /workspace/.npmrc.docker /root/.npmrc

RUN pnpm i --frozen-lockfile
RUN echo "✅ Step 4: 依賴安裝完成"

RUN pnpm build
RUN echo "✅ Step 5: 構建完成"

FROM nginx:1.28.0-alpine AS runner

# 複製構建產物
COPY --from=builder /workspace/dist /usr/share/nginx/html
# 複製所有環境配置文件
COPY --from=builder /workspace/public/configs /usr/share/nginx/html/configs
# 複製 Nginx 配置
COPY --from=builder /workspace/build/nginx/nginx.conf /usr/share/nginx/conf/nginx.conf

# 複製 entrypoint 腳本
COPY --from=builder /workspace/build/docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

EXPOSE 8080

# 使用自定義 entrypoint
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-c", "/usr/share/nginx/conf/nginx.conf", "-g", "daemon off;"]
