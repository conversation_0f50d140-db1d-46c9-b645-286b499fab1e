import basicSsl from '@vitejs/plugin-basic-ssl';
import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { defineConfig } from 'vite';
// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), basicSsl()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    host: '0.0.0.0', // 監聽所有網路街口，而不僅僅是 localhost
    port: 5173,
    allowedHosts: ['dev.mayohr.com', 'tst-apolloxe.mayohr.com'], // 允許這個域名訪問
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
});
