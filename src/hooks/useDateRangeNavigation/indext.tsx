import dayjs from 'dayjs';

import { SelectOption } from '@/types/interface/select';

type DateRange = { startDate: string; endDate: string };

interface UseDateRangeNavigationProps {
  mode: 'month' | 'flexible';
  value: DateRange;
  options: SelectOption<[string, string]>[];
  onChange: (dateRange: DateRange) => void;
}

export function useDateRangeNavigation({
  mode,
  value,
  options,
  onChange,
}: UseDateRangeNavigationProps) {
  if (mode === 'month') {
    // 月曆模式
    const handlePrev = () => {
      if (!value) return;
      const prevMonth = dayjs(value.startDate).subtract(1, 'month');
      onChange({
        startDate: prevMonth.startOf('month').format('YYYY-MM-DD'),
        endDate: prevMonth.endOf('month').format('YYYY-MM-DD'),
      });
    };
    const handleNext = () => {
      if (!value) return;
      const nextMonth = dayjs(value.startDate).add(1, 'month');
      onChange({
        startDate: nextMonth.startOf('month').format('YYYY-MM-DD'),
        endDate: nextMonth.endOf('month').format('YYYY-MM-DD'),
      });
    };
    return { handlePrev, handleNext };
  } else {
    // options 模式
    const idx =
      options?.findIndex(
        opt => opt.value[0] === value?.startDate && opt.value[1] === value?.endDate,
      ) ?? -1;
    const handlePrev = () => {
      if (!options || idx <= 0) return;
      const prev = options[idx - 1];
      onChange({ startDate: prev.value[0], endDate: prev.value[1] });
    };
    const handleNext = () => {
      if (!options || idx < 0 || idx >= options.length - 1) return;
      const next = options[idx + 1];
      onChange({ startDate: next.value[0], endDate: next.value[1] });
    };
    return { handlePrev, handleNext };
  }
}
