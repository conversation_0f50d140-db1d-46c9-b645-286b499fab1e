import { zodResolver } from '@hookform/resolvers/zod';
import dayjs from 'dayjs';
import { isEqual } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { WorkHoursTypeEnum } from '@/types/enums/shiftScheduleApproval';
import { SelectOption } from '@/types/interface/select';

import { ShiftScheduleParams } from '../api/useGetShiftSchedule';

export const SearchBarFormSchema = z.object({
  isShowAllEmployees: z.boolean(),
  department: z.object({
    label: z.string(),
    value: z.string().uuid(),
  }),
  workHoursType: z.object({
    label: z.string(),
    value: z.enum(Object.values(WorkHoursTypeEnum) as [string, ...string[]]),
  }),
  baseDate: z.string().optional(), // 變形工時制才有該欄位
  dateRange: z.object({
    startDate: z.string(),
    endDate: z.string(),
  }),
});

export type SearchBarForm = z.infer<typeof SearchBarFormSchema>;

// 純函數：轉換表單資料為 API 參數
const transformFormToParams = (data: SearchBarForm): ShiftScheduleParams => {
  const params: ShiftScheduleParams = {
    departmentId: data.department.value,
    workHoursType: data.workHoursType.value,
    startDate: dayjs(data.dateRange.startDate).format('YYYY-MM-DD'),
    endDate: dayjs(data.dateRange.endDate).format('YYYY-MM-DD'),
    baseDate: data.baseDate ? dayjs(data.baseDate).format('YYYY-MM-DD') : undefined,
    isShowAllEmployees: data.isShowAllEmployees,
  };

  // 移除 undefined 值
  return Object.fromEntries(
    Object.entries(params).filter(([_, value]) => value !== undefined),
  ) as ShiftScheduleParams;
};

type useSearchBarForm = {
  form: UseFormReturn<SearchBarForm>;
  isFormReady: boolean;
  schedulingParams: ShiftScheduleParams;
};

interface UseSearchBarFormProps {
  departmentOptions: SelectOption<string>[];
  workHoursTypeOptions: SelectOption<WorkHoursTypeEnum>[];
}

export const useSearchBarForm = ({
  departmentOptions,
  workHoursTypeOptions,
}: UseSearchBarFormProps) => {
  const [schedulingParams, setSchedulingParams] = useState<ShiftScheduleParams | undefined>();

  const defaultValues = useMemo((): SearchBarForm => {
    const values = {
      isShowAllEmployees: true, // 預設為 true，表示顯示所有員工
      department: {
        label: departmentOptions[0]?.label ?? '',
        value: (departmentOptions[0]?.value as string) ?? '',
      },
      workHoursType: {
        label: workHoursTypeOptions[0]?.label ?? '',
        value: (workHoursTypeOptions[0]?.value as string) ?? '',
      },
      dateRange: {
        startDate: dayjs().startOf('month').format('YYYY-MM-DD'),
        endDate: dayjs().endOf('month').format('YYYY-MM-DD'),
      },
    };

    return values;
  }, [departmentOptions, workHoursTypeOptions]);

  const form = useForm<SearchBarForm>({
    resolver: zodResolver(SearchBarFormSchema),
    mode: 'onChange',
    defaultValues,
    resetOptions: {
      keepDirtyValues: true,
    },
  });

  // 當 departmentOptions 有資料時，重設表單預設值
  useEffect(() => {
    if (departmentOptions.length > 0) {
      form.reset(defaultValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [departmentOptions.length, workHoursTypeOptions.length]);

  const isFormReady = departmentOptions.length > 0 && workHoursTypeOptions.length > 0;

  const handleParamsChange = useCallback((params: ShiftScheduleParams) => {
    setSchedulingParams(prevParams => {
      // 只有在參數真的變化時才更新
      if (!isEqual(prevParams, params)) {
        return params;
      }
      return prevParams;
    });
  }, []);

  useEffect(() => {
    if (!isFormReady) return;

    // 初始化時手動觸發一次提交
    if (form.formState.isValid) {
      const values = form.getValues();
      const params = transformFormToParams(values);
      handleParamsChange(params);
    }

    const subscription = form.watch(values => {
      if (form.formState.isValid) {
        const params = transformFormToParams(values as SearchBarForm);
        handleParamsChange(params);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, isFormReady, handleParamsChange]);

  return {
    form,
    isFormReady,
    schedulingParams,
  };
};
