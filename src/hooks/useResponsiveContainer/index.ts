import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';

export interface ResponsiveItem {
  id: string;
  [key: string]: unknown;
}

export interface ResponsiveContainerConfig {
  moreButtonWidth?: number;
  itemGap?: number;
  minVisibleItems?: number;
  measureSelector?: string;
  measureAttribute?: string;
}

const DEFAULT_CONFIG: Required<ResponsiveContainerConfig> = {
  moreButtonWidth: 36,
  itemGap: 8,
  minVisibleItems: 1,
  measureSelector: '[data-item-id]',
  measureAttribute: 'data-item-id',
};

export const useResponsiveContainer = <T extends ResponsiveItem>(
  items: T[],
  config: ResponsiveContainerConfig = {},
) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  const [itemWidths, setItemWidths] = useState<Record<string, number>>({});
  const [measuredWidths, setMeasuredWidths] = useState(false);
  const [visibleItemCount, setVisibleItemCount] = useState(4);

  const containerRef = useRef<HTMLDivElement>(null);
  const measureRef = useRef<HTMLDivElement>(null);

  const containerWidth = containerRef.current?.clientWidth || 0;

  // 測量所有按鈕的實際寬度
  const measureItemWidths = useCallback(() => {
    if (!measureRef.current) return;

    const widths: Record<string, number> = {};
    const itemElements = measureRef.current.querySelectorAll(finalConfig.measureSelector);

    itemElements.forEach(element => {
      const id = element.getAttribute(finalConfig.measureAttribute);
      if (id) {
        widths[id] = element.getBoundingClientRect().width;
      }
    });

    setItemWidths(widths);
    setMeasuredWidths(true);
  }, [finalConfig.measureSelector, finalConfig.measureAttribute]);

  // 計算可顯示的按鈕數量（按順序）
  const calculateVisibleItems = useCallback(() => {
    if (!measuredWidths || Object.keys(itemWidths).length === 0) return;

    const { moreButtonWidth, itemGap, minVisibleItems } = finalConfig;
    let totalWidth = 0;
    let visibleCount = 0;

    // Calculate how many items can fit
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const itemWidth = itemWidths[item.id] || 0;
      const widthWithGap = itemWidth + (i > 0 ? itemGap : 0);
      const hasRemainingItems = i < items.length - 1;
      const requiredWidth =
        totalWidth + widthWithGap + (hasRemainingItems ? moreButtonWidth + itemGap : 0);

      if (requiredWidth <= containerWidth) {
        totalWidth += widthWithGap;
        visibleCount++;
      } else {
        break;
      }
    }

    visibleCount = Math.max(minVisibleItems, visibleCount);

    // If all items fit, no need for more button
    if (visibleCount === items.length) {
      setVisibleItemCount(visibleCount);
      return;
    }

    // Adjust for more button space
    let adjustedWidth = 0;
    let adjustedCount = 0;

    for (let i = 0; i < visibleCount; i++) {
      const item = items[i];
      const itemWidth = itemWidths[item.id] || 0;
      const widthWithGap = itemWidth + (i > 0 ? itemGap : 0);

      if (adjustedWidth + widthWithGap + moreButtonWidth + itemGap <= containerWidth) {
        adjustedWidth += widthWithGap;
        adjustedCount++;
      } else {
        break;
      }
    }

    setVisibleItemCount(Math.max(minVisibleItems, adjustedCount));
  }, [containerWidth, itemWidths, measuredWidths, items, finalConfig]);

  // Effects
  // --- ResizeObserver ---
  useEffect(() => {
    if (!containerRef.current) return;
    const observer = new window.ResizeObserver(debounce(measureItemWidths, 100));
    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, [measureItemWidths]);

  // --- window resize (debounced) ---
  useEffect(() => {
    const debounced = debounce(measureItemWidths, 100);
    window.addEventListener('resize', debounced);
    return () => window.removeEventListener('resize', debounced);
  }, [measureItemWidths]);

  // --- items 變動時自動 remeasure ---
  useEffect(() => {
    measureItemWidths();
  }, [items, measureItemWidths]);

  // --- 計算可見按鈕 ---
  useEffect(() => {
    calculateVisibleItems();
  }, [calculateVisibleItems]);

  return {
    // Refs
    containerRef,
    measureRef,

    // State
    visibleItemCount,
    measuredWidths,
    itemWidths,

    // Computed arrays: 將按鈕分為 visible 和 hidden
    visibleItems: items.slice(-visibleItemCount),
    hiddenItems: items.slice(0, -visibleItemCount),

    // Utils
    hasHiddenItems: items.length > visibleItemCount,
    totalItems: items.length,

    // Methods
    remeasure: measureItemWidths,
  };
};
