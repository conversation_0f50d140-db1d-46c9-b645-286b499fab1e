import { useQuery } from '@tanstack/react-query';

import { dashboardAPI } from '@/api/client';

// 儀表板查詢 keys
export const dashboardKeys = {
  all: ['dashboard'] as const,
  stats: () => [...dashboardKeys.all, 'stats'] as const,
};

// 獲取統計資料的 hook
export const useDashboardStats = () => {
  return useQuery({
    queryKey: dashboardKeys.stats(),
    queryFn: () => dashboardAPI.getStats(),
    enabled: true,
    staleTime: 30 * 1000, // 統計資料 30 秒更新一次
    refetchInterval: 5 * 60 * 1000, // 每 5 分鐘自動重新獲取
  });
};
