import { useQuery } from '@tanstack/react-query';
import { AxiosRequestConfig } from 'axios';
import dayjs from 'dayjs';
import { z } from 'zod';

import { apiRequestWithSchema } from '@/api/client';
import { WorkHoursTypeEnum } from '@/types/enums/shiftScheduleApproval';
import { getEnv } from '@/utils/env-config';

const baseDateOptionsSchema = z.array(z.string());

type BaseDateOptions = z.infer<typeof baseDateOptionsSchema>;

const baseDateParamsSchema = z.object({
  departmentId: z.string().uuid(),
  workHoursType: z.enum(Object.values(WorkHoursTypeEnum) as [string, ...string[]]),
});

type BaseDateParams = z.infer<typeof baseDateParamsSchema>;

const baseDateOptionsKey = ['baseDateOptions'] as const;

export function useGetBaseDateOptions(options?: { params?: BaseDateParams; enabled?: boolean }) {
  const { params, enabled } = options || {};
  const ptApi = getEnv('VITE_SERVER_ENV_PT_BACKEND_SERVER');
  const apiUrl = `${ptApi}/EmployeeCalendars/WorkHoursSetting`;

  const config: AxiosRequestConfig = {
    method: 'GET',
    url: apiUrl,
    params,
  };

  const queryInfo = useQuery({
    queryKey: [baseDateOptionsKey, params],
    queryFn: async () => {
      const response = await apiRequestWithSchema<BaseDateOptions>(config, {
        responseSchema: baseDateOptionsSchema,
        paramsSchema: baseDateParamsSchema,
      });

      // 將 response 轉換成 SelectOption 格式
      const selectOptions = response.map(item => ({
        label: dayjs(item).format('YYYY-MM-DD'),
        value: dayjs(item).format('YYYY-MM-DD'),
      }));

      return selectOptions;
    },
    enabled,
  });

  return queryInfo;
}
