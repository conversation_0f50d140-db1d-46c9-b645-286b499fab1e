import { useQuery } from '@tanstack/react-query';
import { AxiosRequestConfig } from 'axios';
import z from 'zod';

import { apiRequestWithSchema } from '@/api/client';
import { getEnv } from '@/utils/env-config';

const userInfoSchema = z.object({
  isVerify: z.boolean(),
  userModule: z.array(z.string()),
  userName: z.string(),
  userRole: z.array(z.string()),
  isSupervisor: z.boolean(),
  isSecretary: z.boolean(),
  personalPicture: z.string(),
  employeeId: z.string(),
  companyId: z.string(),
  companyName: z.string(),
  companyLogoUrl: z.string(),
  companyLogoUpdatedTime: z.string(),
  language: z.string().optional(),
});

type UserInfo = z.infer<typeof userInfoSchema>;

const userInfoKey = ['userInfo'] as const;

export function useGetUserInfo(options: { enabled?: boolean } = {}) {
  const { enabled = true } = options;
  const fdApi = getEnv('VITE_SERVER_ENV_FD_BACKEND_SERVER');
  const apiUrl = `${fdApi}/userInfo`;

  const config: AxiosRequestConfig = {
    method: 'GET',
    url: apiUrl,
  };

  const queryInfo = useQuery({
    queryKey: userInfoKey,
    queryFn: () => apiRequestWithSchema<UserInfo>(config, { responseSchema: userInfoSchema }),
    enabled,
    staleTime: 1000 * 60 * 5, // 5 分鐘後重新獲取
    refetchOnWindowFocus: false, // 不要自動 refetch
  });

  return {
    ...queryInfo,
    data: queryInfo.data,
  };
}
