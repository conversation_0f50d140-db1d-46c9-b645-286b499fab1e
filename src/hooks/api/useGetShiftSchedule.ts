import { useQuery } from '@tanstack/react-query';
import { AxiosRequestConfig } from 'axios';
import { z } from 'zod';

import { apiRequestWithSchema } from '@/api/client';
import { WorkHoursTypeEnum } from '@/types/enums/shiftScheduleApproval';
import { getEnv } from '@/utils/env-config';

// 基本的 UUID 和日期時間 schema
const UuidSchema = z.string().uuid();
const DateTimeSchema = z.string().datetime(); // 使用 ISO 8601 格式
const ColorCodeSchema = z.string().regex(/^#[0-9A-Fa-f]{6}$/); // 用於表示班表的顏色代碼，格式為十六進位色碼 #RRGGBB
const DateRangeSchema = z.object({
  startDate: DateTimeSchema,
  endDate: DateTimeSchema,
});

// 班次的循環週期設定 schema
const CycleSchema = z.object({
  restTimeStart1: DateTimeSchema.nullable(),
  restTimeEnd1: DateTimeSchema.nullable(),
  restTimeStart2: DateTimeSchema.nullable(),
  restTimeEnd2: DateTimeSchema.nullable(),
  restTimeStart3: DateTimeSchema.nullable(),
  restTimeEnd3: DateTimeSchema.nullable(),
  itemOptionId: z.string(),
  cycleSn: z.number(), // 循環週期序號
  cycleStatus: z.number(), // 循環週期狀態
  workOnTime: DateTimeSchema.nullable(),
  workOffTime: DateTimeSchema.nullable(),
});

// 月休假 schema
const MonthLeaveSchema = z.object({
  shiftIds: z.array(UuidSchema),
  cycles: z.null(),
  dayStartTime: DateTimeSchema,
  shiftScheduleId: UuidSchema,
  shiftScheduleName: z.string(),
  colorCode: ColorCodeSchema,
  remnant: z.string().nullable(),
});

// 班次 schema
const ShiftSchema = z.object({
  shiftIds: z.array(UuidSchema), // 班次綁定的班別列表
  cycles: z.array(CycleSchema), // 班次的循環週期設定
  dayStartTime: DateTimeSchema, // 班次日期開始時間
  shiftScheduleId: UuidSchema, // 班次 ID
  shiftScheduleName: z.string(), // 班次名稱
  colorCode: ColorCodeSchema, // 班次顏色代碼
  remnant: z.null(), // 剩餘配額訊息
});

// 班次排程 schema
const ShiftSchedulesSchema = z.object({
  monthLeaves: z.array(MonthLeaveSchema),
  shifts: z.array(ShiftSchema),
});

// 班別排程詳細資料 schema
const ShiftScheduleDetailSchema = z.object({
  restTimeStart1: DateTimeSchema.nullable(),
  restTimeEnd1: DateTimeSchema.nullable(),
  restTimeStart2: DateTimeSchema.nullable(),
  restTimeEnd2: DateTimeSchema.nullable(),
  restTimeStart3: DateTimeSchema.nullable(),
  restTimeEnd3: DateTimeSchema.nullable(),
  shiftScheduleId: UuidSchema,
  cycleSn: z.number(),
  cycleStatus: z.number(),
  colorCode: ColorCodeSchema,
  shiftScheduleName: z.string(),
  isWorkTimeChanged: z.boolean(),
  workOnTime: DateTimeSchema.nullable(),
  workOffTime: DateTimeSchema.nullable(),
  originalWorkOnTime: DateTimeSchema.nullable(),
  originalWorkOffTime: DateTimeSchema.nullable(),
  restMinutes: z.number().nullable(),
});

const CalendarEventSchema = z
  .object({
    eventStatus: z.number(),
    itemOptionId: z.string().nullable(),
  })
  .catchall(z.any());

// 日曆項目 schema
const CalendarSchema = z.object({
  date: DateTimeSchema, // 該筆排班的日期
  shiftId: UuidSchema.nullable(), // 該筆班次的 UUID
  cycleSn: z.number(), // 當天的排班循環序號
  billingStatus: z.number(),
  isBilling: z.boolean(),
  shiftSchedule: ShiftScheduleDetailSchema.nullable(),
  supportDeptId: z.string().nullable(), // 跨部門支援時的部門 ID
  supportDeptName: z.string().nullable(), // 跨部門支援時的部門名稱
  calendarEvent: CalendarEventSchema.nullable(), // 當天如果有特殊事件，會包含事件詳細資訊
  leaveSheets: z.array(z.unknown()).nullable(), // 當天請假單列表，無請假時為空陣列
  overtimeSheets: z.array(z.unknown()).nullable(), // 當天加班單列表，無加班時為空陣列
  partialSupport: z.array(z.unknown()).nullable(), // 當天支援單列表，無支援時為空陣列
  tripSheets: z.array(z.unknown()).nullable(), // 當天出差單列表，無出差時為空陣列
  selectShiftSchedule: z.boolean(), // 是否已選擇排班
  arrangeLeave: z.boolean(), // 是否已安排休假
  isShiftRules: z.boolean(), // 是否按照排班規則進行
  adjustmentScheduleTime: z.boolean(), // 是否允許調整排班時間
  advanceLeave: z.boolean(), // 是否已申請預先休假
  isEditable: z.boolean(), // 是否允許編輯該天排班
  isAllLeaveRequestFormsApproved: z.boolean().nullable(), // 是否所有請假申請表均已核准
  itemOptionId: z.string().nullable(), // 項目選項 ID
  finalBilling: z.boolean(), // 是否已完成最終計費
  clockedInTime: DateTimeSchema.nullable(), // 上班打卡時間
  dayStartTime: DateTimeSchema, // 該天排班起始時間
  latestUpdateTime: DateTimeSchema,
  latestUpdaterName: z.string().nullable(),
  schedulingStatus: z.number(), // 該天排班狀態，0: 無排班，1: 有排班（需進一步判斷循環狀態），3: 例假日
});

// 排程狀態範圍 schema
const SchedulingStatusRangeMapSchema = z.record(z.string(), z.array(DateRangeSchema));

// 員工 schema
// FIXME: API 尚缺欄位「職稱」、「圖片」
const EmployeeSchema = z.object({
  chineseName: z.string(), // 員工姓名
  employeeNumber: z.string(), // 員工編號
  schedulingStatus: z.number(), // 排班狀態，用於判斷是否可編輯等
  totalMonthLeaveTime: z.number(),
  totalScheduleLeaveTime: z.number(),
  fatigueValue: z.number(),
  schedulingStatusRangeMap: SchedulingStatusRangeMapSchema,
  empSchedulingStartDate: DateTimeSchema,
  empSchedulingEndDate: DateTimeSchema,
  supervisorSchedulingStartDate: DateTimeSchema,
  supervisorSchedulingEndDate: DateTimeSchema,
  selectShiftSchedule: z.boolean(),
  arrangeLeave: z.boolean(),
  isShiftRules: z.boolean(),
  adjustmentScheduleTime: z.boolean(),
  advanceLeave: z.boolean(),
  isEditable: z.boolean(),
  isSupervisorEditable: z.boolean(),
  calendars: z.array(CalendarSchema),
  monthLeaves: z.array(z.unknown()).nullable(),
  employeeId: UuidSchema,
  totalWorkTime: z.number(),
  totalLeaveTime: z.number(),
  totalOverTime: z.number(),
  totalTripMinutes: z.number(),
  monthLeaveDays: z.number(),
  monthLeaveDaysUsed: z.number(),
  defaultWorkHours: z.number(),
  vacationDays: z.number(),
});

// 支援員工 schema
const SupportEmployeeSchema = z.object({
  date: DateTimeSchema,
  supportHours: z.number(),
  employees: z.array(EmployeeSchema), // TODO 待確認
});

// 班表排程儀表板 schema
const ShiftScheduleDashBoardSchema = z.object({
  shiftScheduleId: UuidSchema,
  colorCode: ColorCodeSchema,
  shiftScheduleName: z.string(),
  workTimes: z.array(z.number()), // 每天的工時（以小時為單位）
  supportTimes: z.array(z.number()), // 每天的支援工時（以小時為單位）
  totals: z.array(z.number()), // 每天的總工時（以小時為單位）
});

// 請假儀表板 schema
const LeaveDashBoardSchema = z.object({
  date: DateTimeSchema,
  vacationTime: z.number(), // 當天的休假時間，以小時為單位（例如：8 小時）。
  leaveTime: z.number(), // 當天的請假時間，以小時為單位（例如：8 小時）。
  total: z.number(), // 當天的總時間，通常是 VacationTime 和 LeaveTime 的總和（例如：8 小時）。
});

// 資料部分 schema
const ShiftScheduleResponseSchema = z.object({
  isEditable: z.boolean(),
  isSchedulingEditable: z.boolean(),
  latestUpdateTime: DateTimeSchema.nullable(),
  latestUpdaterName: z.string().nullable(),
  schedulingStatus: z.number(), // 該天排班狀態
  schedulingStatusInfo: z.string().nullable(), // TODO 待確認
  deployTime: DateTimeSchema.nullable(),
  deployerName: z.string().nullable(),
  shiftSchedules: ShiftSchedulesSchema,
  supportEmployees: z.array(SupportEmployeeSchema),
  employees: z.array(EmployeeSchema),
  shiftScheduleDashBoard: z.array(ShiftScheduleDashBoardSchema),
  leaveDashBoard: z.array(LeaveDashBoardSchema),
});
type ShiftScheduleRes = z.infer<typeof ShiftScheduleResponseSchema>;
export type Employee = z.infer<typeof EmployeeSchema>;
export type Calendar = z.infer<typeof CalendarSchema>;
export type Shift = z.infer<typeof ShiftSchema>;
export type ShiftScheduleDashBoard = z.infer<typeof ShiftScheduleDashBoardSchema>;
export type LeaveDashBoard = z.infer<typeof LeaveDashBoardSchema>;
export type ShiftScheduleDetail = z.infer<typeof ShiftScheduleDetailSchema>;
export type CalendarEvent = z.infer<typeof CalendarEventSchema>;

// 取得班表 API 參數
const ShiftScheduleParamsSchema = z.object({
  departmentId: z.string().uuid(),
  startDate: z.string(),
  endDate: z.string(),
  workHoursType: z.enum(Object.values(WorkHoursTypeEnum) as [string, ...string[]]),
  baseDate: z.string().optional(),
  isShowAllEmployees: z.boolean().optional(),
});

export type ShiftScheduleParams = z.infer<typeof ShiftScheduleParamsSchema>;

const shiftScheduleKey = ['shiftSchedule'] as const;

export function useGetShiftSchedule(options?: {
  params?: ShiftScheduleParams;
  enabled?: boolean;
  filter?: Record<string, string>;
}) {
  const { params, enabled, filter } = options || {};
  const ptApi = getEnv('VITE_SERVER_ENV_PT_BACKEND_SERVER');
  const apiUrl = `${ptApi}/EmployeeCalendars/deptScheduling/V2`;

  const config: AxiosRequestConfig = {
    method: 'GET',
    url: apiUrl,
    params,
  };

  const queryInfo = useQuery({
    queryKey: [shiftScheduleKey, params, filter],
    queryFn: async () => {
      const response = await apiRequestWithSchema<ShiftScheduleRes>(config, {
        // responseSchema: ShiftScheduleResponseSchema, // FIXME：加上這個 schema 就不會過，待確認原因
        paramsSchema: ShiftScheduleParamsSchema,
      });

      // 如果有 filter，過濾 employees
      if (filter && response.employees) {
        response.employees = response.employees.filter(employee => {
          return filter.employeeId === employee.employeeId;
        });
      }
      return response;
    },
    staleTime: 2 * 60 * 1000,
    enabled,
  });

  return queryInfo;
}
