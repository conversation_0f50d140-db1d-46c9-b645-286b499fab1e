import { useQuery } from '@tanstack/react-query';

import { storeAPI } from '@/api/client';

// 門店查詢 keys
export const storeKeys = {
  all: ['stores'] as const,
  lists: () => [...storeKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...storeKeys.lists(), filters] as const,
  details: () => [...storeKeys.all, 'detail'] as const,
  detail: (id: string) => [...storeKeys.details(), id] as const,
};

// 獲取門店列表的 hook
export const useStores = (params?: { page?: number; limit?: number; isActive?: boolean }) => {
  return useQuery({
    queryKey: storeKeys.list(params || {}),
    queryFn: () => storeAPI.getStores(params),
    enabled: true,
    staleTime: 5 * 60 * 1000, // 門店資料變化較少，可以快取久一點
  });
};

// 獲取單個門店詳情的 hook
export const useStore = (id: string) => {
  return useQuery({
    queryKey: storeKeys.detail(id),
    queryFn: () => storeAPI.getStore(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};
