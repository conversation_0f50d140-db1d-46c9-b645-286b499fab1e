import { useQuery } from '@tanstack/react-query';
import { AxiosRequestConfig } from 'axios';
import { z } from 'zod';

import { apiRequestWithSchema } from '@/api/client';
import { WorkHoursTypeEnum } from '@/types/enums/shiftScheduleApproval';
import { getEnv } from '@/utils/env-config';

const departmentsResponseSchema = z.array(
  z.object({
    departmentId: z.string().uuid(),
    deptCode: z.string(),
    deptCName: z.string().nullable(),
    deptEName: z.string().nullable(),
  }),
);

const departmentsRequestParamsSchema = z.object({
  departmentId: z.string().uuid(),
  workHoursType: z.enum(Object.values(WorkHoursTypeEnum) as [string, ...string[]]),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  baseDate: z.string().optional(),
});

export type ScheduleDepartments = z.infer<typeof departmentsResponseSchema>;

const scheduleDepartmentsKey = ['scheduleDepartments'] as const;

export function useGetScheduleDepartments() {
  const ptApi = getEnv('VITE_SERVER_ENV_PT_BACKEND_SERVER');
  const apiUrl = `${ptApi}/departments/scheduling`;

  const config: AxiosRequestConfig = {
    method: 'GET',
    url: apiUrl,
  };

  const queryInfo = useQuery({
    queryKey: scheduleDepartmentsKey,
    queryFn: () =>
      apiRequestWithSchema<ScheduleDepartments>(config, {
        paramsSchema: departmentsRequestParamsSchema,
        responseSchema: departmentsResponseSchema,
      }),
    staleTime: 5 * 60 * 1000,
  });

  return {
    ...queryInfo,
    data: queryInfo.data,
  };
}
