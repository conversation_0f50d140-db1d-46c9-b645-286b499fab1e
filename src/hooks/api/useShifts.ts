import { useQuery } from '@tanstack/react-query';

import { shiftAPI } from '@/api/client';
import { ShiftQueryParams } from '@/mocks/types';

// 排班查詢 keys
export const shiftKeys = {
  all: ['shifts'] as const,
  lists: () => [...shiftKeys.all, 'list'] as const,
  list: (filters: ShiftQueryParams | undefined) => [...shiftKeys.lists(), filters] as const,
  details: () => [...shiftKeys.all, 'detail'] as const,
  detail: (id: string) => [...shiftKeys.details(), id] as const,
};

// 獲取排班列表的 hook
export const useShifts = (params?: ShiftQueryParams) => {
  return useQuery({
    queryKey: shiftKeys.list(params),
    queryFn: () => shiftAPI.getShifts(params),
    enabled: true,
    staleTime: 2 * 60 * 1000, // 排班資料變化較頻繁，快取時間短一點
  });
};

// 獲取單個排班詳情的 hook
export const useShift = (id: string) => {
  return useQuery({
    queryKey: shiftKeys.detail(id),
    queryFn: () => shiftAPI.getShift(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  });
};
