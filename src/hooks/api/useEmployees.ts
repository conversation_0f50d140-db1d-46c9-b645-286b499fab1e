import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { employeeAPI } from '@/api/client';
import { ApiResponse, Employee, EmployeeCreateRequest, EmployeeUpdateRequest } from '@/mocks/types';

// 查詢 keys - 統一管理，避免拼寫錯誤
export const employeeKeys = {
  all: ['employees'] as const,
  lists: () => [...employeeKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...employeeKeys.lists(), filters] as const,
  details: () => [...employeeKeys.all, 'detail'] as const,
  detail: (id: string) => [...employeeKeys.details(), id] as const,
};

// 獲取員工列表的 hook
export const useEmployees = (params?: {
  page?: number;
  limit?: number;
  department?: string;
  isActive?: boolean;
}) => {
  return useQuery({
    queryKey: employeeKeys.list(params || {}),
    queryFn: () => employeeAPI.getEmployees(params),
    // 只有在需要時才啟用查詢
    enabled: true,
    // 資料 5 分鐘內被認為是新鮮的
    staleTime: 5 * 60 * 1000,
  });
};

// 獲取單個員工詳情的 hook
export const useEmployee = (id: string) => {
  return useQuery({
    queryKey: employeeKeys.detail(id),
    queryFn: () => employeeAPI.getEmployee(id),
    enabled: !!id, // 只有當有 id 時才執行查詢
    staleTime: 5 * 60 * 1000,
  });
};

// 創建員工的 mutation hook
export const useCreateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: EmployeeCreateRequest) => employeeAPI.createEmployee(data),
    // 成功後的處理
    onSuccess: (newEmployee: ApiResponse<Employee>) => {
      // 無效化員工列表，觸發重新獲取
      queryClient.invalidateQueries({
        queryKey: employeeKeys.lists(),
      });

      // 可選：將新員工直接加入快取，避免重新獲取
      queryClient.setQueryData(employeeKeys.detail(newEmployee.data.id), newEmployee);
    },
    // 錯誤處理
    onError: error => {
      console.error('創建員工失敗:', error);
      // 這裡可以加入錯誤通知邏輯
    },
  });
};

// 更新員工的 mutation hook
export const useUpdateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: EmployeeUpdateRequest }) =>
      employeeAPI.updateEmployee(id, data),
    // 樂觀更新
    onMutate: async ({ id, data }) => {
      // 取消任何進行中的查詢，避免競爭條件
      await queryClient.cancelQueries({ queryKey: employeeKeys.detail(id) });

      // 獲取之前的值作為回滾備份
      const previousEmployee = queryClient.getQueryData(employeeKeys.detail(id));

      // 樂觀更新：立即更新UI
      queryClient.setQueryData(employeeKeys.detail(id), (old: ApiResponse<Employee> | undefined) =>
        old ? { ...old, data: { ...old.data, ...data } } : old,
      );

      // 返回上下文，供錯誤時回滾使用
      return { previousEmployee };
    },
    onError: (_err, { id }, context) => {
      // 發生錯誤時回滾
      if (context?.previousEmployee) {
        queryClient.setQueryData(employeeKeys.detail(id), context.previousEmployee);
      }
    },
    onSettled: (_data, _error, { id }) => {
      // 無論成功或失敗，都重新獲取資料確保一致性
      queryClient.invalidateQueries({ queryKey: employeeKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: employeeKeys.lists() });
    },
  });
};

// 刪除員工的 mutation hook
export const useDeleteEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => employeeAPI.deleteEmployee(id),
    onSuccess: (_, deletedId) => {
      // 從快取中移除該員工
      queryClient.removeQueries({ queryKey: employeeKeys.detail(deletedId) });
      // 無效化列表
      queryClient.invalidateQueries({ queryKey: employeeKeys.lists() });
    },
  });
};
