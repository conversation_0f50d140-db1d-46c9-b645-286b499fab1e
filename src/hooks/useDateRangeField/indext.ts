import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

import { findTodayPeriodOption, generatePeriodOptions } from '@/pages/ShiftScheduleApproval/utils';
import { WorkHoursTypeEnum } from '@/types/enums/shiftScheduleApproval';
import { SelectOption } from '@/types/interface/select';

import { SearchBarForm } from '../useSearchBarForm';

// 純函數：檢查是否為標準工時
const isStandardWorkHour = (workHoursType: WorkHoursTypeEnum): boolean => {
  return workHoursType === WorkHoursTypeEnum.STANDARD;
};

// 純函數：格式化日期範圍選項
const formatDateRangeOptions = (
  baseDate: string,
  workHoursType: WorkHoursTypeEnum,
): SelectOption<[string, string]>[] => {
  return generatePeriodOptions(baseDate, workHoursType);
};

// 純函數：找到預設選項
const findDefaultOption = (
  options: SelectOption<[string, string]>[],
): { startDate: string; endDate: string } | null => {
  const matchedOption = findTodayPeriodOption(options);
  return matchedOption
    ? {
        startDate: matchedOption.value[0],
        endDate: matchedOption.value[1],
      }
    : null;
};

interface UseDateRangeFieldProps {
  onDateRangeChange?: (dateRange: { startDate: string; endDate: string }) => void;
}

export const useDateRangeField = ({ onDateRangeChange }: UseDateRangeFieldProps = {}) => {
  const [options, setOptions] = useState<SelectOption<[string, string]>[]>([]);

  const { control, setValue } = useFormContext<SearchBarForm>();

  const baseDate = useWatch({ control, name: 'baseDate' });
  const workHoursType = useWatch({ control, name: 'workHoursType' });

  // 生成日期範圍選項並設置預設值
  const generateAndSetDateRangeOptions = useCallback(
    (baseDate: string, workHoursType: WorkHoursTypeEnum) => {
      const newOptions = formatDateRangeOptions(baseDate, workHoursType);
      setOptions(newOptions);

      const defaultOption = findDefaultOption(newOptions);
      if (defaultOption) {
        setValue('dateRange', defaultOption);
        onDateRangeChange?.(defaultOption);
      }
    },
    [setValue, onDateRangeChange],
  );

  // 監聽工時類型和基準日期變化
  useEffect(() => {
    if (!workHoursType?.value) return;

    if (isStandardWorkHour(workHoursType?.value as WorkHoursTypeEnum)) {
      // 標準工時：dateRange = 當月 1 號 ~ 當月最後一天
      const startDate = dayjs().startOf('month').format('YYYY-MM-DD');
      const endDate = dayjs().endOf('month').format('YYYY-MM-DD');
      setValue('dateRange', { startDate, endDate });
      if (baseDate !== undefined) setValue('baseDate', undefined);
      onDateRangeChange?.({ startDate, endDate });
      setOptions([]); // 標準工時不需要 options
    } else if (baseDate) {
      // 變型工時
      generateAndSetDateRangeOptions(baseDate, workHoursType.value as WorkHoursTypeEnum);
    }
  }, [baseDate, workHoursType, generateAndSetDateRangeOptions, onDateRangeChange, setValue]);

  return {
    options,
    workHoursType,
    isStandardWorkHour: isStandardWorkHour(workHoursType?.value as WorkHoursTypeEnum),
  };
};
