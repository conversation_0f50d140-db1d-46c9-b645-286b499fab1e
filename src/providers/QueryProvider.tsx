'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ReactNode, useState } from 'react';

interface QueryProviderProps {
  children: ReactNode;
}

// 定義帶有狀態碼的錯誤類型
interface ErrorWithStatus extends Error {
  status?: number;
}

export default function QueryProvider({ children }: QueryProviderProps) {
  // 使用 useState 確保 QueryClient 只被創建一次
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // 資料被認為是新鮮的時間 (5分鐘)
            staleTime: 5 * 60 * 1000,
            // 資料在快取中保留的時間 (10分鐘) - 新版本使用 gcTime
            gcTime: 10 * 60 * 1000,
            // 重試邏輯
            retry: (failureCount, error: Error) => {
              // 不重試 404 錯誤
              const errorWithStatus = error as ErrorWithStatus;
              if (errorWithStatus.status === 404) return false;
              return failureCount < 3;
            },
            // 網路重新連接時重新獲取
            refetchOnReconnect: true,
            // 視窗重新聚焦時重新獲取
            refetchOnWindowFocus: false, // 設為 false 避免過於頻繁的請求
          },
          mutations: {
            // 變更操作失敗時重試一次
            retry: 1,
          },
        },
      }),
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* 只在開發環境顯示 DevTools */}
      {typeof window !== 'undefined' && import.meta.env.DEV && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}
