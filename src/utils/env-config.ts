/**
 * env.ts
 *
 * Utility for type-safe access to Vite environment variables (VITE_*)
 *
 * 用途：
 * - 統一管理所有 VITE_ 開頭的環境變數 key
 * - 提供型別安全的 getEnv(key) 方法，避免拼錯 key
 *
 * 支援的 key：
 * - 只要加在 ALL_KEYS array 裡就會有型別提示
 *
 * 用法：
 *   import { getEnv } from '@/utils/env';
 *   const apiBase = getEnv('VITE_SERVER_ENV_BACKEND_SERVER');
 *   const ptApi = getEnv('VITE_SERVER_ENV_PT_BACKEND_SERVER');
 *
 * 注意：
 * - 只支援 ALL_KEYS 裡面列出的 key，新增變數請同步加進 ALL_KEYS
 */

export const ALL_KEYS = [
  'VITE_SERVER_ENV_DEVELOPMENT_ENV_VARIABLE',
  'VITE_SERVER_ENV',
  'VITE_SERVER_ENV_WEB_SITE',
  'VITE_SERVER_ENV_AUTH_WEB_SITE',
  'VITE_SERVER_ENV_BACKEND_SERVER',
  'VITE_SERVER_ENV_FD_BACKEND_SERVER',
  'VITE_SERVER_ENV_PT_BACKEND_SERVER',
  'VITE_SERVER_ENV_PT_REPORT_BACKEND_SERVER',
  'VITE_SERVER_ENV_PY_BACKEND_SERVER',
  'VITE_SERVER_ENV_AUTH_LOGIN_URL',
] as const;

type EnvKey = (typeof ALL_KEYS)[number];

/**
 * 獲取環境變數
 *
 * 優先從運行時配置獲取，如果不存在則回退到編譯時的環境變數
 *
 * @param key 環境變數鍵名
 * @returns 環境變數值
 */
export function getEnv(key: EnvKey): string {
  // 移除 'VITE_' 前綴以匹配 RUNTIME_CONFIG 中的鍵
  const runtimeKey = key.replace('VITE_', '');

  // 優先從運行時配置獲取
  if (
    typeof window !== 'undefined' &&
    window.RUNTIME_CONFIG &&
    window.RUNTIME_CONFIG[runtimeKey as keyof typeof window.RUNTIME_CONFIG]
  ) {
    return window.RUNTIME_CONFIG[runtimeKey as keyof typeof window.RUNTIME_CONFIG] || '';
  }

  // 回退到編譯時的環境變數
  return import.meta.env[key] || '';
}
