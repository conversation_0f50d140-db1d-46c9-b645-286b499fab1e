/**
 * Cookie 操作工具
 *
 * 使用 react-cookie 套件來操作 cookie
 *
 * 使用方式：
 * 1. 引入 cookies 實例
 * 2. 使用 getCookie、setCookie、removeCookie 方法來操作 cookie
 * 3. 使用時請參考 CookieNameEnum 來管理 cookie 名稱
 *
 */
import { Cookies } from 'react-cookie';

export const cookies = new Cookies();
export const getCookie = (name: string) => cookies.get(name) ?? null;
export const setCookie = (name: string, value: string) => cookies.set(name, value);
export const removeCookie = (name: string) => cookies.remove(name);
