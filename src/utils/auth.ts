/**
 * 認證相關工具函數
 */

import { CookieNameEnum } from '@/types/enums/common/cookie';
import { getCookie } from '@/utils/cookie';
import { getEnv } from '@/utils/env-config';

/**
 * 獲取登入頁面 URL
 * @returns 登入頁面 URL
 */
export function getLoginUrl(): string {
  const authLoginUrl = getEnv('VITE_SERVER_ENV_AUTH_LOGIN_URL');

  // 如果環境變數中有配置，直接使用
  if (authLoginUrl) {
    return authLoginUrl;
  }

  // 否則使用預設的測試環境 URL
  const currentUrl = encodeURIComponent(window.location.href);
  const lang = getCurrentLanguage();

  return `https://tst-auth.mayohr.com/HRM/Account/Login?original_target=${currentUrl}&lang=${lang}`;
}

/**
 * 獲取當前語言設置
 * @returns 當前語言代碼
 */
export function getCurrentLanguage(): string {
  // 從 localStorage 獲取語言設置
  const storedLang = localStorage.getItem('i18nextLng');
  if (storedLang) {
    return storedLang;
  }

  // 從 cookie 獲取語言設置
  const cookieLang = getCookie('locale');
  if (cookieLang) {
    return cookieLang;
  }

  // 從瀏覽器語言獲取
  const browserLang = navigator.language;
  if (browserLang) {
    return browserLang.split('-')[0];
  }

  // 預設語言
  return 'zh';
}

/**
 * 檢查用戶是否已登入
 * @returns 是否已登入
 */
export function isUserLoggedIn(): boolean {
  return Boolean(getCookie(CookieNameEnum.AUTH_TOKEN));
}

/**
 * 檢查是否為 iframe 模式
 * @returns 是否為 iframe 模式
 */
export function isIframeMode(): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    return window.self !== window.top;
  } catch {
    // 如果無法訪問 window.top，說明是 iframe 模式
    return true;
  }
}
