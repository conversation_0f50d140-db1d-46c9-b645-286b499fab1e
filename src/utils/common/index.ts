/**
 * 將字串轉換為駝峰式命名（camelCase）
 * 支援 snake_case、kebab-case、PascalCase 轉 camelCase
 * 例如：'user_name' -> 'userName', 'User_Name' -> 'userName', 'user-name' -> 'userName', 'IsSupervisor' -> 'isSupervisor'
 *
 * @param str 欲轉換的字串
 * @returns 轉換後的 camelCase 字串
 */
export const toCamelCaseString = (str: string): string => {
  // 先將下劃線或連字號後的字母轉為大寫（如 user_name -> userName, user-name -> userName）
  const s = str.replace(/[_-]([a-zA-Z])/g, (_, group1) => group1.toUpperCase());
  // 再將首字母轉為小寫（如 IsSupervisor -> isSupervisor, User_Name -> userName）
  return s.charAt(0).toLowerCase() + s.slice(1);
};

/**
 * 遞迴將物件（或陣列）所有 key 轉換為 camelCase
 * - 支援巢狀物件與陣列
 * - 非物件/陣列型別會原樣回傳
 *
 * @param obj 欲轉換的物件、陣列或其他型別
 * @returns key 已轉為 camelCase 的新物件/陣列/原值
 */
export function toCamelCase(obj: unknown): unknown {
  // 如果是陣列，遞迴處理每個元素
  if (Array.isArray(obj)) {
    return obj.map(toCamelCase);
    // 如果是物件，遞迴處理每個 key/value
  } else if (obj !== null && typeof obj === 'object') {
    return Object.fromEntries(
      // 取出所有 key/value，key 轉 camelCase，value 遞迴處理
      Object.entries(obj as Record<string, unknown>).map(([k, v]) => [
        toCamelCaseString(k),
        toCamelCase(v),
      ]),
    );
  }
  // 不是物件/陣列，直接回傳原值
  return obj;
}
