// 環境變數
export enum EnvironmentEnum {
  DEV = 'dev',
  TST = 'tst',
  UAT = 'uat',
  PREPRD = 'preprd',
  PROD = 'prod',
}

// i18n 多語系
export enum LanguagesEnum {
  TW = 'zh-TW',
  CN = 'zh-CN',
  HK = 'zh-HK',
  US = 'en-US',
  VI = 'vi-VN',
}

export const LanguageMenuNamesEnum: Record<LanguagesEnum, string> = {
  [LanguagesEnum.US]: 'English',
  [LanguagesEnum.TW]: '繁體中文',
  [LanguagesEnum.CN]: '简体中文',
  [LanguagesEnum.HK]: '中文(香港)',
  [LanguagesEnum.VI]: 'Tiếng Việt',
};
