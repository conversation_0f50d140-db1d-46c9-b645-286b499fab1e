// 工時制
export enum WorkHoursTypeEnum {
  STANDARD = 'StandardWorkHours', // 標準工時
  TWO_WEEKS = 'TwoWeeksWorkHours', // 雙週變形工時
  FOUR_WEEKS = 'FourWeeksWorkHours', // 四週變形工時
  EIGHT_WEEKS = 'EightWeeksWorkHours', // 八週變形工時
}

// 常用的枚舉類型
export enum SchedulingStatus {
  UNKNOWN = 0,
  ACTIVE = 1,
  INACTIVE = 2,
  HOLIDAY = 3,
}

export enum CycleStatus {
  WORK_DAY = 1,
  HOLIDAY = 2,
}

/**
 * 定義班別週期的類型
 */
export enum CycleTypeEnum {
  /** 上班 (週期) */
  WORK_DAY = 'CY00001',
  /** 休假 (週期) */
  VACATION = 'CY00002',
  /** 休息日 (週期) */
  REST = 'CY00003',
  /** 例假日 (週期) */
  OFFICIAL = 'CY00004',
}

/**
 * 定義行事曆日的特殊類型
 */
export enum CalendarDayTypeEnum {
  /** 特殊休假日 */
  HOLIDAY = '00001',
  /** 特殊上班日 */
  WORK_DAY = '00002',
  /** 假日出勤給薪 */
  PAY_DAY = '00003',
  /** 休息日 (行事曆) */
  REST_DAY = '00004',
  /** 例假日 (行事曆) */
  OFFICIAL_HOLIDAY = '00005',
  /** 國定假日 */
  NATIONAL_HOLIDAY = '00006',
}

export enum BillingStatus {
  NOT_BILLED = 0,
  BILLED = 1,
}

// 常用的工具類型
export type EmployeeId = string;
export type ShiftId = string;
export type ShiftScheduleId = string;
export type DateString = string; // ISO 8601 格式
export type ColorCode = string; // 十六進制顏色代碼

// 常用的選擇器類型
export interface EmployeeSelector {
  employeeNumber?: string;
  chineseName?: string;
}

export interface DateRangeSelector {
  startDate: DateString;
  endDate: DateString;
}

export interface ShiftSelector {
  shiftScheduleId?: string;
  shiftScheduleName?: string;
}
