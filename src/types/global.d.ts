/**
 * 全局類型定義
 */

declare global {
  interface Window {
    /**
     * 運行時配置對象
     * 在容器啟動時由 docker-entrypoint.sh 腳本生成
     */
    RUNTIME_CONFIG?: {
      SERVER_ENV_DEVELOPMENT_ENV_VARIABLE?: string;
      SERVER_ENV?: string;
      SERVER_ENV_WEB_SITE?: string;
      SERVER_ENV_AUTH_WEB_SITE?: string;
      SERVER_ENV_BACKEND_SERVER?: string;
      SERVER_ENV_FD_BACKEND_SERVER?: string;
      SERVER_ENV_PT_BACKEND_SERVER?: string;
      SERVER_ENV_PT_REPORT_BACKEND_SERVER?: string;
      SERVER_ENV_PY_BACKEND_SERVER?: string;
    };
  }
}

export {};
