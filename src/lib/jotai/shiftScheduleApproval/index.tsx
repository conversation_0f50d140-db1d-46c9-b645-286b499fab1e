import { atom } from 'jotai';

interface ShiftScheduleAtom {
  viewMode: ViewModeEnum;
}

export enum ViewModeEnum {
  VIEW = 'view',
  EDIT = 'edit',
}

// 基礎 Atom
export const shiftScheduleAtom = atom<ShiftScheduleAtom>({
  viewMode: ViewModeEnum.VIEW,
});

// 衍生的 read-only atom
export const viewModeAtom = atom(get => get(shiftScheduleAtom).viewMode);

// 衍生的 action atom
export const toggleViewModeAtom = atom(
  null, // 不需要 read
  (get, set) => {
    const currentSchedule = get(shiftScheduleAtom);
    const newViewMode =
      currentSchedule.viewMode === ViewModeEnum.VIEW ? ViewModeEnum.EDIT : ViewModeEnum.VIEW;

    set(shiftScheduleAtom, {
      ...currentSchedule,
      viewMode: newViewMode,
    });
  },
);
