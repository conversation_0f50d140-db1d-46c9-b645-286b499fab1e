import { atom } from 'jotai';

export interface UserInfoAtom {
  isVerify: boolean;
  userModule: string[];
  userName: string;
  userRole: string[];
  isSupervisor: boolean;
  isSecretary: boolean;
  personalPicture: string;
  employeeId: string;
  companyId: string;
  companyName: string;
  companyLogoUrl: string;
  companyLogoUpdatedTime: string;
  language?: string;
}

export const userInfoAtom = atom<UserInfoAtom | null>(null);
