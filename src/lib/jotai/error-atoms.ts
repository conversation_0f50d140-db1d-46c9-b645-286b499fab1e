import { atom } from 'jotai';

import type { ApiError } from '@/api/axios-instance';

// 錯誤類型定義
export interface AppError {
  id: string;
  type: 'api' | 'network' | 'runtime' | 'route' | 'chunk';
  title: string;
  message: string;
  code?: string | number;
  timestamp: number;
  isRetryable: boolean;
  retryCount?: number;
  maxRetries?: number;
  apiError?: ApiError;
  stack?: string;
}

// 錯誤通知類型
export interface ErrorNotification {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number; // 自動消失時間（毫秒），undefined 表示不自動消失
  actions?: Array<{
    label: string;
    action: () => void;
    variant?: 'primary' | 'secondary';
  }>;
}

// 全局錯誤狀態原子
export const globalErrorAtom = atom<AppError | null>(null);

// 錯誤歷史記錄原子
export const errorHistoryAtom = atom<AppError[]>([]);

// 錯誤通知列表原子
export const errorNotificationsAtom = atom<ErrorNotification[]>([]);

// 網路狀態原子
export const networkStatusAtom = atom<'online' | 'offline'>('online');

// 重試狀態原子
export const retryStatusAtom = atom<{
  isRetrying: boolean;
  retryingErrorId?: string;
}>({
  isRetrying: false,
});

// 錯誤統計原子
export const errorStatsAtom = atom<{
  totalErrors: number;
  apiErrors: number;
  networkErrors: number;
  runtimeErrors: number;
}>({
  totalErrors: 0,
  apiErrors: 0,
  networkErrors: 0,
  runtimeErrors: 0,
});

// 派生原子：當前是否有錯誤
export const hasErrorAtom = atom(get => get(globalErrorAtom) !== null);

// 派生原子：當前是否有通知
export const hasNotificationsAtom = atom(get => get(errorNotificationsAtom).length > 0);

// 派生原子：是否為網路錯誤
export const isNetworkErrorAtom = atom(get => {
  const error = get(globalErrorAtom);
  return error?.type === 'network' || error?.apiError?.isNetworkError === true;
});

// 派生原子：是否可重試
export const canRetryAtom = atom(get => {
  const error = get(globalErrorAtom);
  const retryStatus = get(retryStatusAtom);

  if (!error || retryStatus.isRetrying) return false;

  return error.isRetryable && (error.retryCount || 0) < (error.maxRetries || 3);
});

// 動作原子：設置全局錯誤
export const setGlobalErrorAtom = atom(null, (get, set, error: AppError | null) => {
  set(globalErrorAtom, error);

  if (error) {
    // 添加到歷史記錄
    const history = get(errorHistoryAtom);
    set(errorHistoryAtom, [error, ...history.slice(0, 49)]); // 保留最近50個錯誤

    // 更新統計
    const stats = get(errorStatsAtom);
    set(errorStatsAtom, {
      totalErrors: stats.totalErrors + 1,
      apiErrors: stats.apiErrors + (error.type === 'api' ? 1 : 0),
      networkErrors: stats.networkErrors + (error.type === 'network' ? 1 : 0),
      runtimeErrors: stats.runtimeErrors + (error.type === 'runtime' ? 1 : 0),
    });
  }
});

// 動作原子：清除全局錯誤
export const clearGlobalErrorAtom = atom(null, (get, set) => {
  set(globalErrorAtom, null);
  set(retryStatusAtom, { isRetrying: false });
});

// 動作原子：添加錯誤通知
export const addErrorNotificationAtom = atom(
  null,
  (get, set, notification: Omit<ErrorNotification, 'id'>) => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newNotification: ErrorNotification = { ...notification, id };

    const notifications = get(errorNotificationsAtom);
    set(errorNotificationsAtom, [...notifications, newNotification]);

    // 如果設置了自動消失時間，則設置定時器
    if (notification.duration) {
      setTimeout(() => {
        const currentNotifications = get(errorNotificationsAtom);
        set(
          errorNotificationsAtom,
          currentNotifications.filter(n => n.id !== id),
        );
      }, notification.duration);
    }
  },
);

// 動作原子：移除錯誤通知
export const removeErrorNotificationAtom = atom(null, (get, set, notificationId: string) => {
  const notifications = get(errorNotificationsAtom);
  set(
    errorNotificationsAtom,
    notifications.filter(n => n.id !== notificationId),
  );
});

// 動作原子：清除所有通知
export const clearAllNotificationsAtom = atom(null, (get, set) => {
  set(errorNotificationsAtom, []);
});

// 動作原子：設置重試狀態
export const setRetryStatusAtom = atom(
  null,
  (get, set, status: { isRetrying: boolean; retryingErrorId?: string }) => {
    set(retryStatusAtom, status);
  },
);

// 動作原子：更新網路狀態
export const setNetworkStatusAtom = atom(null, (get, set, status: 'online' | 'offline') => {
  set(networkStatusAtom, status);

  // 如果網路恢復，清除網路相關錯誤
  if (status === 'online') {
    const currentError = get(globalErrorAtom);
    if (currentError?.type === 'network') {
      set(globalErrorAtom, null);
    }
  }
});

// 工具函數：創建錯誤對象
export const createAppError = (
  type: AppError['type'],
  title: string,
  message: string,
  options: Partial<Omit<AppError, 'id' | 'type' | 'title' | 'message' | 'timestamp'>> = {},
): AppError => ({
  id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  type,
  title,
  message,
  timestamp: Date.now(),
  isRetryable: false,
  ...options,
});

// 工具函數：從 API 錯誤創建應用錯誤
export const createAppErrorFromApiError = (
  apiError: ApiError,
  retryCount = 0,
  maxRetries = 3,
): AppError => {
  const type = apiError.isNetworkError ? 'network' : 'api';

  return createAppError(type, getErrorTitle(apiError.code), apiError.message, {
    code: apiError.code,
    isRetryable: apiError.isRetryable,
    retryCount,
    maxRetries,
    apiError,
  });
};

// 工具函數：獲取錯誤標題
const getErrorTitle = (code: string): string => {
  switch (code) {
    case 'NETWORK_ERROR':
      return '網路連接失敗';
    case 'TIMEOUT_ERROR':
      return '請求超時';
    case 'UNAUTHORIZED':
      return '未授權訪問';
    case 'FORBIDDEN':
      return '權限不足';
    case 'NOT_FOUND':
      return '資源未找到';
    case 'INTERNAL_SERVER_ERROR':
      return '服務器錯誤';
    case 'SERVICE_UNAVAILABLE':
      return '服務不可用';
    default:
      return '發生錯誤';
  }
};
