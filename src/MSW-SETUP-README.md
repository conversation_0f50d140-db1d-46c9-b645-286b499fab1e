# MSW (Mock Service Worker) 設置指南

## 📦 需要安裝的套件

請在專案根目錄執行以下命令安裝 MSW 相關套件：

```bash
# 安裝 MSW 核心套件
pnpm add -D msw

# 如果使用 dayjs 處理日期 (如果尚未安裝)
pnpm add dayjs
```

## 🚀 初始化 MSW

安裝完套件後，需要生成 MSW 的 Service Worker 文件：

```bash
# 在 public 目錄生成 mockServiceWorker.js
npx msw init public/ --save
```

這會在 `public/mockServiceWorker.js` 創建 MSW 的 Service Worker 文件。

## 📁 專案結構

MSW 相關文件已經為您創建，結構如下：

```
├── src/
│   ├── api/
│   │   └── client.ts                # API 客戶端
│   ├── mocks/
│   │   ├── types.ts                 # API 相關類型定義
│   │   ├── handlers.ts              # MSW request handlers
│   │   ├── browser.ts               # 瀏覽器環境配置
│   │   ├── node.ts                  # Node.js 環境配置 (測試用)
│   │   └── data/
│   │       ├── index.ts             # 統一導出
│   │       ├── employees.ts         # 員工 mock 數據
│   │       ├── stores.ts            # 門店 mock 數據
│   │       └── shifts.ts            # 排班 mock 數據
│   └── ...
├── public/
│   └── mockServiceWorker.js
├── package.json
├── pnpm-lock.yaml
└── ...
```

## 🔧 環境配置

在 `src/main.tsx` 中已經配置了 MSW 的條件性啟動：

```typescript
// 檢查是否需要啟動 MSW
const enableMSW = true; // 在開發環境啟用

async function enableMocking() {
  if (!enableMSW) return;
  
  if (typeof window !== 'undefined') {
    const { startMSW } = await import('./mocks/browser');
    return startMSW();
  }
}
```

## 📡 可用的 API Endpoints

MSW 已為您設置了以下 API endpoints：

### 員工管理
- `GET /api/employees` - 獲取員工列表 (支援分頁和篩選)
- `GET /api/employees/:id` - 獲取單一員工
- `POST /api/employees` - 創建員工
- `PUT /api/employees/:id` - 更新員工
- `DELETE /api/employees/:id` - 刪除員工

### 門店管理
- `GET /api/stores` - 獲取門店列表
- `GET /api/stores/:id` - 獲取單一門店

### 排班管理
- `GET /api/shifts` - 獲取排班列表 (支援多種篩選條件)
- `GET /api/shifts/:id` - 獲取單一排班
- `POST /api/shifts` - 創建排班
- `PUT /api/shifts/:id` - 更新排班
- `DELETE /api/shifts/:id` - 刪除排班

### 班次類型
- `GET /api/shift-types` - 獲取班次類型列表

### 統計資訊
- `GET /api/dashboard/stats` - 獲取儀表板統計數據

## 🎮 使用方式

### 1. 使用 API 客戶端

```typescript
import { employeeAPI, storeAPI, shiftAPI } from '@/api/client';

// 獲取員工列表
const employees = await employeeAPI.getEmployees({ page: 1, limit: 10 });

// 創建新員工
const newEmployee = await employeeAPI.createEmployee({
  name: '張三',
  department: '銷售部',
  position: '銷售專員',
  color: '#4F46E5'
});

// 獲取排班數據
const shifts = await shiftAPI.getShifts({
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  employeeId: '1'
});
```

### 2. 查看演示頁面

專案已包含 `MSWDemoPage` 組件，您可以：

1. 將其添加到路由中
2. 查看所有 API 的實際運作
3. 測試創建、讀取功能

### 3. 在現有組件中使用

將現有的硬編碼數據替換為 API 調用：

```typescript
// 之前
const employees = [/* 硬編碼數據 */];

// 現在
const [employees, setEmployees] = useState([]);

useEffect(() => {
  employeeAPI.getEmployees().then(response => {
    setEmployees(response.data);
  });
}, []);
```

## 🧪 測試環境配置

對於單元測試和集成測試，可以使用 Node.js 環境的 MSW：

```typescript
import { setupMSWForTesting } from '@/mocks/node';

// 在測試文件中
describe('API Tests', () => {
  setupMSWForTesting();
  
  // 您的測試...
});
```

## 🔧 自定義和擴展

### 添加新的 API endpoint

1. 在 `src/mocks/types.ts` 中定義新的類型
2. 在 `src/mocks/data/` 中添加 mock 數據
3. 在 `src/mocks/handlers.ts` 中添加新的 handler
4. 在 `src/api/client.ts` 中添加對應的 API 函數

### 調整 mock 數據

編輯 `src/mocks/data/` 目錄下的文件來修改 mock 數據。

### 控制 MSW 啟用/停用

在 `src/main.tsx` 中修改 `enableMSW` 變數：

```typescript
const enableMSW = process.env.NODE_ENV === 'development';
```

## 📝 注意事項

1. **Service Worker**: 確保 `public/mockServiceWorker.js` 文件存在
2. **HTTPS**: 在生產環境中，Service Worker 需要 HTTPS
3. **快取**: 瀏覽器可能會快取 Service Worker，開發時可能需要清除快取
4. **相容性**: MSW 需要現代瀏覽器支援 Service Worker

## 🚀 下一步

1. 安裝必要的套件
2. 初始化 MSW Service Worker
3. 啟動開發伺服器
4. 訪問 MSW Demo 頁面測試功能
5. 將現有組件遷移到使用 MSW API

MSW 現在已經完全配置好，可以為您的排班管理系統提供完整的 mock API 支援！ 
