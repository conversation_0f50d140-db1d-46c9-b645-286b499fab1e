import { MaskLoading } from '@mayo/mayo-ui-beta';
import { lazy, Suspense } from 'react';
import type { RouteObject } from 'react-router-dom';

const MSWDemoPage = lazy(() => import(/* ChunkName: "MSWDemoPage" */ '@/pages/MSWDemoPage'));

const mswDemoRoute: RouteObject = {
  path: 'msw-demo',
  element: (
    <Suspense fallback={<MaskLoading />}>
      <MSWDemoPage />
    </Suspense>
  ),
};

export default mswDemoRoute;
