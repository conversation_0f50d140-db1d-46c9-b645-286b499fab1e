import { MaskLoading } from '@mayo/mayo-ui-beta';
import { lazy, Suspense } from 'react';
import { RouteObject } from 'react-router-dom';

import { BasicDemoAction, BasicDemoLoader } from '../pages/BasicDemoPage';

const BasicDemoPage = lazy(() => import(/* ChunkName: "BasicDemoPage" */ '@/pages/BasicDemoPage'));

const basicDemoRoute: RouteObject = {
  path: 'basic-demo',
  element: (
    <Suspense fallback={<MaskLoading />}>
      <BasicDemoPage />
    </Suspense>
  ),
  loader: BasicDemoLoader,
  action: BasicDemoAction,
};

export default basicDemoRoute;
