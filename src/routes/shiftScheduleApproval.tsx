import { MaskLoading } from '@mayo/mayo-ui-beta';
import { lazy, Suspense } from 'react';
import type { RouteObject } from 'react-router-dom';

const ShiftScheduleApprovalPage = lazy(
  () => import(/* ChunkName: "ShiftScheduleApprovalPage" */ '@/pages/ShiftScheduleApproval'),
);

const shiftScheduleApprovalRoute: RouteObject = {
  path: 'shift-schedule-approval',
  element: (
    <Suspense fallback={<MaskLoading />}>
      <ShiftScheduleApprovalPage />
    </Suspense>
  ),
};

export default shiftScheduleApprovalRoute;
