import { MaskLoading } from '@mayo/mayo-ui-beta';
import { lazy, Suspense } from 'react';
import type { RouteObject } from 'react-router-dom';

const ShiftSchedulerPage = lazy(
  () => import(/* ChunkName: "ShiftSchedulerPage" */ '@/pages/ShiftSchedulerPage'),
);

const shiftSchedulerRoute: RouteObject = {
  path: 'shift-scheduler',
  element: (
    <Suspense fallback={<MaskLoading />}>
      <ShiftSchedulerPage />
    </Suspense>
  ),
};

export default shiftSchedulerRoute;
