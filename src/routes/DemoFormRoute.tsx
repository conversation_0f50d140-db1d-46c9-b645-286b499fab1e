import { MaskLoading } from '@mayo/mayo-ui-beta';
import { lazy, Suspense } from 'react';
import { RouteObject } from 'react-router-dom';

const DemoFormPage = lazy(() => import(/* ChunkName: "DemoFormPage" */ '@/pages/DemoFormPage'));

const demoFormRoute: RouteObject = {
  path: 'demo-form',
  element: (
    <Suspense fallback={<MaskLoading />}>
      <DemoFormPage />
    </Suspense>
  ),
};

export default demoFormRoute;
