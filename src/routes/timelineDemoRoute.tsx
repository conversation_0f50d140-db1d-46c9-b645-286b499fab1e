import { MaskLoading } from '@mayo/mayo-ui-beta';
import { lazy, Suspense } from 'react';
import type { RouteObject } from 'react-router-dom';

const TimelineDemoPage = lazy(
  () => import(/* ChunkName: "TimelineDemoPage" */ '@/pages/TimelineDemoPage'),
);

const timelineDemoRoute: RouteObject = {
  path: 'timeline-demo',
  element: (
    <Suspense fallback={<MaskLoading />}>
      <TimelineDemoPage />
    </Suspense>
  ),
};

export default timelineDemoRoute;
