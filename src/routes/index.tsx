import { Outlet, type RouteObject } from 'react-router-dom';

import { AuthGuard } from '@/components/auth';
import { NotFoundPage, RouteErrorBoundary } from '@/components/error';
import Layout from '@/components/layout';

import basicDemoRoute from './basicDemoRoute';
import demoFormRoute from './DemoFormRoute';
import errorTestRoute from './errorTestRoute';
import mswDemoRoute from './mswDemoRoute';
import shiftScheduleApprovalRoute from './shiftScheduleApproval';
import shiftSchedulerRoute from './shiftSchedulerRoute';
import timelineDemoRoute from './timelineDemoRoute';

const defaultRoute = { ...timelineDemoRoute, path: '' };

const appRoutes: RouteObject[] = [
  {
    path: '/',
    element: (
      <AuthGuard>
        <Layout>
          <Outlet />
        </Layout>
      </AuthGuard>
    ),
    errorElement: <RouteErrorBoundary />,
    children: [
      defaultRoute,
      timelineDemoRoute,
      shiftSchedulerRoute,
      mswDemoRoute,
      basicDemoRoute,
      demoFormRoute,
      shiftScheduleApprovalRoute,
      errorTestRoute,
    ],
  },
  // 404 頁面 - 捕獲所有未匹配的路由
  {
    path: '*',
    element: <NotFoundPage />,
  },
];

export default appRoutes;
