import { Button, CompoundDropdownMenu, CompoundDropdownMenuItem } from '@mayo/mayo-ui-beta/v2';
import { Globe } from 'lucide-react';
import { useState, useTransition } from 'react';
import { useCookies } from 'react-cookie';

import { changeLanguage, fallbackLng, supportedLngs } from '@/i18n';
import { LanguageMenuNamesEnum, LanguagesEnum } from '@/types/enums/common';
import { CookieNameEnum } from '@/types/enums/common/cookie';

const LanguageDropdown = () => {
  // 從 cookie 中讀取當前語系
  const [cookies, setCookie] = useCookies([CookieNameEnum.LOCALE]);
  const getCurrentLanguage = () => cookies[CookieNameEnum.LOCALE] || fallbackLng;

  const [locale, setLocale] = useState(getCurrentLanguage());

  const [, startTransition] = useTransition();

  const handleLanguageChange = async (newLocale: string) => {
    console.log('🚀 ~ handleLanguageChange ~ newLocale:', newLocale);
    startTransition(() => {
      // 清除 form auth cookie
      //   deleteCookie(COOKIE_NAMES.AUTH, '.mayohr.com');
      //   deleteCookie(COOKIE_NAMES.REFRESH_TOKEN, '.mayohr.com');

      //   setCookie(COOKIE_NAMES.LOCALE, newLocale, '.mayohr.com');
      //   axios.defaults.headers.common['Accept-Language'] = newLocale;
      //   window.location.reload();
      setCookie(CookieNameEnum.LOCALE, newLocale);
      setLocale(newLocale);
      changeLanguage(newLocale as LanguagesEnum);
      // window.location.reload();
    });
  };

  const languageItems = supportedLngs.map(lng => ({
    id: `radio-${lng}`,
    label: LanguageMenuNamesEnum[lng],
    value: lng,
  }));

  const menuItems: CompoundDropdownMenuItem[] = [
    { id: 'label-language', type: 'label', label: 'Language Family' },
    { id: 'sep-1', type: 'separator' },
    {
      id: 'radio-language',
      type: 'radioGroup',
      label: 'languageSelection',
      value: locale,
      onValueChange: handleLanguageChange,
      items: languageItems,
    },
  ];

  return (
    <CompoundDropdownMenu
      menuItems={menuItems}
      triggerLabel="open"
      dropdownMenuContentProps={{
        className: 'border-sidebar-border',
      }}
      placement="bottom-end"
      triggerProps={{ variant: 'outline', className: 'w-52' }}
      trigger={
        <Button size="icon" variant="default" className="bg-foreground hover:bg-accent">
          <Globe height={18} width={18} />
        </Button>
      }
    />
  );
};

export default LanguageDropdown;
