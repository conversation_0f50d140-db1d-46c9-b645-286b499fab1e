import {
  Button,
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@mayo/mayo-ui-beta/v2';
import { Grid2X2 } from 'lucide-react';
import React, { forwardRef } from 'react';

import { cn } from '@/utils/style';

const ListItem = forwardRef<React.ElementRef<'a'>, React.ComponentPropsWithoutRef<'a'>>(
  ({ className, title, children, ...props }, ref) => (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            'block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground',
            className,
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none text-primary-foreground">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-gray-300">{children}</p>
        </a>
      </NavigationMenuLink>
    </li>
  ),
);

const Linkup = () => (
  <NavigationMenu navigationMenuViewportProviderClassName="absolute left-[-240px] lg:left-[-440px]">
    <NavigationMenuList>
      <NavigationMenuItem>
        <NavigationMenuTrigger>
          <Button size="icon" variant="default" className="mt-1.5 bg-foreground hover:bg-accent">
            <Grid2X2 height={18} width={18} />
          </Button>
        </NavigationMenuTrigger>
        <NavigationMenuContent>
          <ul className="grid gap-3 p-4 md:w-[300px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
            <li className="row-span-3">
              <NavigationMenuLink asChild>
                <a
                  className="flex h-full w-full select-none flex-col justify-end rounded-md bg-accent from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                  href="/"
                >
                  <div className="mb-2 mt-4 text-lg font-medium text-primary-foreground">
                    shadcn/ui
                  </div>
                  <p className="text-sm leading-tight text-gray-300">
                    Beautifully designed components built with Radix UI and Tailwind CSS.
                  </p>
                </a>
              </NavigationMenuLink>
            </li>
            <ListItem href="/docs" title="Introduction">
              Re-usable components built using Radix UI and Tailwind CSS.
            </ListItem>
            <ListItem href="/docs/installation" title="Installation">
              How to install dependencies and structure your app.
            </ListItem>
            <ListItem href="/docs/primitives/typography" title="Typography">
              Styles for headings, paragraphs, lists...etc
            </ListItem>
          </ul>
        </NavigationMenuContent>
      </NavigationMenuItem>
    </NavigationMenuList>
  </NavigationMenu>
);

export default Linkup;
