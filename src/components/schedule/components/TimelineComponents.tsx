import { Checkbox } from '@mayo/mayo-ui-beta/v2';
import React, { CSSProperties } from 'react';

import { ItemRendererProps, TimelineGroup } from '../types';

// ===== 時間軸項目渲染器 =====

/**
 * 時間軸項目渲染器元件
 * 負責渲染時間軸上的班次項目
 */
export const TimelineItemRenderer: React.FC<ItemRendererProps> = ({
  item,
  itemContext,
  getItemProps,
  getResizeProps,
}) => {
  const resizeProps = getResizeProps ? getResizeProps() : { left: null, right: null };
  const leftResizeProps = resizeProps.left as React.HTMLAttributes<HTMLDivElement> | null;
  const rightResizeProps = resizeProps.right as React.HTMLAttributes<HTMLDivElement> | null;

  // 根據選中狀態設置背景色
  const backgroundColor = itemContext.selected
    ? '#f9a825' // 選中時的顏色
    : item.itemProps?.style?.backgroundColor;

  // 获取props对象
  const itemProps = getItemProps({
    style: {
      backgroundColor,
      color: item.itemProps?.style?.color || 'white',
      borderRadius: '4px',
      boxShadow: '0 1px 2px rgba(0,0,0,0.15)',
      display: 'flex',
      flexDirection: 'column',
      padding: '2px 6px',
      overflow: 'hidden',
      borderLeft: '4px solid #3B82F6',
      position: 'sticky',
      top: 0,
    },
  });

  // 移除key属性，单独传递
  const { key, ...itemPropsWithoutKey } = itemProps as { key?: React.Key } & Record<
    string,
    unknown
  >;

  return (
    <div key={key} {...itemPropsWithoutKey}>
      <div className="flex flex-col items-center justify-center p-1">
        {/* 項目標題 */}
        <div className="text-sm font-medium">{item.title}</div>
        {/* 顯示分店信息 */}
        <div className="text-xs opacity-80">{item.store}</div>
      </div>
      {/* 調整大小控制器 */}
      {leftResizeProps && <div {...leftResizeProps} />}
      {rightResizeProps && <div {...rightResizeProps} />}
    </div>
  );
};

// ===== 時間軸群組渲染器 =====

/**
 * 時間軸群組渲染器元件的屬性
 */
export interface TimelineGroupRendererProps {
  /** 群組資料 */
  group: TimelineGroup;
  /** 複選框變更處理函數 */
  onCheckboxChange: (groupId: string, isChecked: boolean) => void;
}

/**
 * 時間軸群組渲染器元件
 * 負責渲染左側群組列表的每一個群組項目
 */
export const TimelineGroupRenderer: React.FC<TimelineGroupRendererProps> = ({
  group,
  onCheckboxChange,
}) => {
  return (
    <div className="flex h-full flex-col items-center justify-center px-3 py-1">
      <div className="w-full truncate text-center text-sm font-medium">
        <Checkbox
          checked={group.selected}
          onValueChange={() => onCheckboxChange(group.id, !group.selected)}
        >
          {group.title}
        </Checkbox>
      </div>
    </div>
  );
};

// ===== 時間軸側邊欄標題 =====

/**
 * 從 react-calendar-timeline 中的 GetRootProps 類型
 */
export type GetRootProps = (props?: { style?: CSSProperties }) => Record<string, unknown>;

/**
 * 側邊欄標題元件的屬性
 */
export interface TimelineSidebarHeaderProps {
  /** 是否全選 */
  isAllSelected: boolean;
  /** 全選/取消全選處理函數 */
  onSelectAllChange: () => void;
  /** 獲取根元素屬性的函數 */
  getRootProps: GetRootProps;
}

/**
 * 時間軸側邊欄標題元件
 * 負責渲染左側群組列表的標題欄，包含全選功能
 */
export const TimelineSidebarHeader: React.FC<TimelineSidebarHeaderProps> = ({
  isAllSelected,
  onSelectAllChange,
  getRootProps,
}) => {
  return (
    <div
      {...getRootProps({
        style: {
          backgroundColor: '#f9fafb',
          padding: '0.5rem',
          border: '1px solid #e5e',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          color: 'black',
        },
      })}
    >
      <Checkbox
        className="mr-5"
        name="text-checkbox1"
        checked={isAllSelected}
        onValueChange={onSelectAllChange}
      >
        Select All
      </Checkbox>
    </div>
  );
};
