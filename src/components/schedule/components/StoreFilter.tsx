import { Select } from '@mayo/mayo-ui-beta/v2';
import { useAtomValue, useSetAtom } from 'jotai';
import React, { useCallback, useMemo } from 'react';

import { selectedStoresAtom, updateSelectedStoresAtom } from '../atoms/timelineItemsAtom';
import { stores } from './TimelineData';
// 定義選項類型
type StoreOption = {
  label: string;
  value: string;
  description?: string;
};

// 在組件外部預先計算門店選項，因為這是基於不變的數據
const storeOptions: StoreOption[] = stores.map(store => ({
  label: store.name,
  value: store.id,
  description: store.location,
}));

export const StoreFilter: React.FC = () => {
  // 獲取已選門店和更新函數
  const selectedStoreIds = useAtomValue(selectedStoresAtom);
  const updateSelectedStores = useSetAtom(updateSelectedStoresAtom);

  // 計算當前選中的選項
  // 只有當 selectedStoreIds 真正變化時才需要重新計算
  const selectedOptions = useMemo(
    () => storeOptions.filter(option => selectedStoreIds.includes(option.value)),
    [selectedStoreIds],
  );

  // 處理門店選擇變更
  const handleStoreChange = useCallback(
    (selectedOptions: StoreOption[] | null) => {
      if (!selectedOptions) {
        // 清空選擇
        updateSelectedStores([]);
      } else {
        // 更新選中的門店 ID
        const selectedIds = selectedOptions.map(option => option.value);
        updateSelectedStores(selectedIds);
      }
    },
    [updateSelectedStores],
  );

  return (
    <div className="mb-3">
      <label className="mb-1 block text-sm font-medium text-gray-700">門店篩選</label>

      {/* 下拉選擇框 */}
      <Select
        options={storeOptions}
        value={selectedOptions}
        onChange={handleStoreChange}
        isClearable={true}
        isSearchable={true}
        isDisabled={false}
        isMulti={true}
        isError={false}
        placeholder="請選擇門店"
        noOptionsMessage={() => '無符合選項'}
        size="base"
      />
    </div>
  );
};
