import dayjs from 'dayjs';
import { useAtomValue, useSetAtom } from 'jotai';
import React, { useCallback, useEffect } from 'react';
import Timeline, {
  CustomHeader,
  DateHeader,
  SidebarHeader,
  TimelineHeaders,
} from 'react-calendar-timeline';

import {
  currentViewAtom,
  timeChangeAtom,
  timeRangeAtom,
  timeStepsAtom,
} from '../atoms/timelineAtoms';
import {
  filteredTimelineItemsAtom,
  groupsItemsAtom,
  moveTimelineItemAtom,
  resizeTimelineItemAtom,
  selectedEmployeeAtom,
  setGroupsAtom,
  updateSelectedEmployeeAtom,
} from '../atoms/timelineItemsAtom';
import { CustomViews, Views } from '../constants/timelineConstants';
import { TimelineGroup, TimelineItem } from '../types';
import {
  TimelineGroupRenderer,
  TimelineItemRenderer,
  TimelineSidebarHeader,
} from './TimelineComponents';

type TimelineContentProps = {
  timelineGroups: TimelineGroup[];
  timelineItems?: TimelineItem[]; // 設為可選，因為現在使用atom
};

/**
 * 時間軸內容元件
 * 整合時間軸功能，處理數據和事件
 */
export const TimelineContent: React.FC<TimelineContentProps> = ({ timelineGroups }) => {
  // 時間相關狀態
  const currentView = useAtomValue(currentViewAtom);
  const timeRange = useAtomValue(timeRangeAtom);
  const timeSteps = useAtomValue(timeStepsAtom);
  const handleTimeChange = useSetAtom(timeChangeAtom);

  // 員工與項目相關狀態
  const setGroups = useSetAtom(setGroupsAtom);
  const updateSelectedEmployee = useSetAtom(updateSelectedEmployeeAtom);
  const selectedEmployee = useAtomValue(selectedEmployeeAtom);
  const filteredGroups = useAtomValue(groupsItemsAtom);
  const filteredItems = useAtomValue(filteredTimelineItemsAtom);

  // 項目操作相關函數
  const moveItem = useSetAtom(moveTimelineItemAtom);
  const resizeItem = useSetAtom(resizeTimelineItemAtom);

  // 初始化時設置所有員工組
  useEffect(() => {
    setGroups(
      timelineGroups.map(group => ({
        ...group,
        selected: false,
      })),
    );
  }, [timelineGroups, setGroups]);

  // 全選/取消全選處理函數
  const handleSelectAllChange = useCallback(() => {
    if (selectedEmployee?.length === filteredGroups.length) {
      // 已全選，則清除選擇
      updateSelectedEmployee([]);
    } else {
      // 未全選，則全選
      updateSelectedEmployee(filteredGroups.map(group => group.id.toString()));
    }
  }, [selectedEmployee, filteredGroups, updateSelectedEmployee]);

  // 處理群組勾選變更
  const handleCheckboxChange = useCallback(
    (groupId: string, isChecked: boolean) => {
      if (isChecked) {
        const newSelectedEmployee = [...selectedEmployee, groupId];
        updateSelectedEmployee(newSelectedEmployee);
      } else {
        const newSelectedEmployee = selectedEmployee.filter(employee => employee !== groupId);
        updateSelectedEmployee(newSelectedEmployee);
      }
    },
    [selectedEmployee, updateSelectedEmployee],
  );

  // 項目移動處理
  const handleItemMove = useCallback(
    (itemId: string | number, dragTime: number, newGroupOrder: number) => {
      moveItem({
        itemId,
        dragTime,
        newGroupOrder,
        groups: timelineGroups,
      });
    },
    [moveItem, timelineGroups],
  );

  // 項目調整大小處理
  const handleItemResize = useCallback(
    (itemId: string | number, time: number, edge: 'left' | 'right') => {
      resizeItem({
        itemId,
        time,
        edge,
      });
    },
    [resizeItem],
  );

  // 群組渲染器包裝
  const groupRenderer = useCallback(
    ({ group }: { group: TimelineGroup }) => {
      return <TimelineGroupRenderer group={group} onCheckboxChange={handleCheckboxChange} />;
    },
    [handleCheckboxChange],
  );

  // 自定義頭部渲染
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderCustomHeader = useCallback((props: any) => {
    const { headerContext, getRootProps, getIntervalProps } = props;
    const { intervals } = headerContext;

    return (
      <div
        {...getRootProps({
          style: {
            backgroundColor: '#f9fafb',
            padding: '0.5rem',
            border: '1px solid #e5e',
            height: '50px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          },
        })}
      >
        {intervals.map(
          (
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            interval: any,
          ) => {
            // 創建唯一key
            const intervalKey = interval.startTime.valueOf().toString();

            const intervalProps = getIntervalProps({ interval });

            // 使用析構賦值時需聲明變數來滿足eslint
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { key: _key, ...propsWithoutKey } = intervalProps;

            return (
              <div key={intervalKey} {...propsWithoutKey}>
                <div className="flex items-center justify-center py-1">
                  <div className="text-lg font-medium">{interval.startTime.format('DD')}</div>
                  <div className="text-lg text-gray-500">{interval.startTime.format('ddd')}</div>
                </div>
              </div>
            );
          },
        )}
      </div>
    );
  }, []);

  // 側邊欄頭部渲染

  const renderSidebarHeader = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (props: any) => {
      const { getRootProps } = props;
      const isAllSelected = selectedEmployee?.length === filteredGroups.length;

      return (
        <TimelineSidebarHeader
          isAllSelected={isAllSelected}
          onSelectAllChange={handleSelectAllChange}
          getRootProps={getRootProps}
        />
      );
    },
    [selectedEmployee, filteredGroups, handleSelectAllChange],
  );

  // 時間變更處理
  const handleVisibleTimeChange = useCallback(
    (visibleTimeStart: number, visibleTimeEnd: number) => {
      handleTimeChange({ visibleTimeStart, visibleTimeEnd });
    },
    [handleTimeChange],
  );

  return (
    <Timeline
      // 組與項目數據
      groups={filteredGroups}
      items={filteredItems}
      // 自定義渲染
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      itemRenderer={TimelineItemRenderer as any}
      groupRenderer={groupRenderer}
      // 時間範圍
      visibleTimeStart={timeRange.start}
      visibleTimeEnd={timeRange.end}
      onTimeChange={handleVisibleTimeChange}
      // 佈局配置
      sidebarWidth={100}
      itemTouchSendsClick={false}
      itemHeightRatio={0.75}
      // 互動配置
      canMove
      canResize="both"
      stackItems
      timeSteps={timeSteps}
      // 縮放配置
      minZoom={24 * 60 * 60 * 1000}
      maxZoom={30 * 24 * 60 * 60 * 1000}
      // 事件處理
      onItemMove={handleItemMove}
      onItemResize={handleItemResize}
      onItemSelect={itemId => {
        console.log('Item selected', itemId);
      }}
      onCanvasClick={(groupId, time) => {
        console.log('Canvas clicked', { groupId, time });
      }}
      // 視覺配置
      lineHeight={60}
      traditionalZoom
      horizontalLineClassNamesForGroup={() => ['custom-group-line']}
      verticalLineClassNamesForTime={() => ['custom-time-line']}
      style={{ backgroundColor: 'white' }}
      className="timeline-component"
    >
      <TimelineHeaders>
        <SidebarHeader>{renderSidebarHeader}</SidebarHeader>

        {/* 月視圖、雙週視圖和四週視圖的標題 */}
        {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          [Views.MONTH, CustomViews.FOURWEEK, CustomViews.BIWEEK].includes(currentView as any) && (
            <>
              {/* 月份標題 */}
              <DateHeader
                unit="month"
                labelFormat={([startTime]) => dayjs(startTime).format('MM')}
              />
              {/* 日期標題 */}
              <DateHeader unit="day" labelFormat={([startTime]) => dayjs(startTime).format('DD')} />
            </>
          )
        }

        {/* 週視圖的標題 */}
        {currentView === Views.WEEK && (
          <>
            {/* 月份標題 */}
            <DateHeader unit="month" labelFormat={([startTime]) => dayjs(startTime).format('MM')} />
            {/* 日期標題 */}
            <CustomHeader unit="day">{renderCustomHeader}</CustomHeader>
          </>
        )}

        {/* 日視圖的標題 */}
        {currentView === Views.DAY && (
          <>
            {/* 日期與星期 */}
            <CustomHeader unit="day">{renderCustomHeader}</CustomHeader>
            {/* 小時標題 */}
            <DateHeader unit="hour" labelFormat={([startTime]) => dayjs(startTime).format('HH')} />
          </>
        )}
      </TimelineHeaders>
    </Timeline>
  );
};
