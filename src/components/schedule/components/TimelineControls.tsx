import { DateRangePicker } from '@mayo/mayo-ui-beta/v2';
import dayjs from 'dayjs';
import { useAtomValue, useSetAtom } from 'jotai';
import React, { useCallback } from 'react';
import { View, Views } from 'react-big-calendar';

import {
  currentViewAtom,
  timeChangeAtom,
  timeRangeAtom,
  viewDependentTimeStepsAtom,
} from '../atoms/timelineAtoms';
import { CustomViews } from '../types';
import { StoreFilter } from './StoreFilter';
import { ViewSwitcher } from './ViewSwitcher';
export const TimelineControls: React.FC = () => {
  // 狀態管理
  const currentView = useAtomValue(currentViewAtom);
  const timeRange = useAtomValue(timeRangeAtom);
  const setTimeChange = useSetAtom(timeChangeAtom);
  const updateView = useSetAtom(viewDependentTimeStepsAtom);

  // 處理視圖切換
  const handleViewChange = useCallback(
    (view: View | CustomViews) => {
      updateView(view);
    },
    [updateView],
  );

  // 處理日期選擇變更
  const handleTimeChange = useCallback(
    (dateStrings: [string, string]) => {
      console.log('🚀 ~ dateStrings:', dateStrings);
      if (!dateStrings || dateStrings.length !== 2) return;

      const [startStr, endStr] = dateStrings;
      const start = dayjs(startStr);
      const end = dayjs(endStr);

      setTimeChange({
        visibleTimeStart: start.startOf('day').valueOf(),
        visibleTimeEnd: end.endOf('day').valueOf(),
      });
    },
    [setTimeChange],
  );

  // 處理週期間移動操作
  const performWeekAction = useCallback(
    (action: 'prev' | 'next' | 'current') => {
      const today = dayjs();
      let newStart;

      switch (action) {
        case 'prev':
          newStart = dayjs(timeRange.start).subtract(7, 'day');
          break;
        case 'next':
          newStart = dayjs(timeRange.start).add(7, 'day');
          break;
        case 'current':
          newStart = today.startOf('week');
          break;
        default:
          return;
      }

      setTimeChange({
        visibleTimeStart: newStart.valueOf(),
        visibleTimeEnd: newStart.add(7, 'day').valueOf(),
      });
    },
    [timeRange.start, setTimeChange],
  );

  // 週操作處理函數
  const handlePrevWeek = useCallback(() => performWeekAction('prev'), [performWeekAction]);
  const handleCurrentWeek = useCallback(() => performWeekAction('current'), [performWeekAction]);
  const handleNextWeek = useCallback(() => performWeekAction('next'), [performWeekAction]);

  // 日期選擇器的值
  const datePickerValue = [
    dayjs(timeRange.start).format('YYYY/MM/DD'),
    dayjs(timeRange.end).subtract(1, 'day').format('YYYY/MM/DD'),
  ];

  return (
    <>
      {/* 視圖選擇器區域 */}
      <div className="flex flex-col items-start justify-between bg-gray-100 p-2">
        <ViewSwitcher view={currentView} onViewChange={handleViewChange} />
        <StoreFilter />
      </div>

      {/* 週切換按鈕區域 */}
      <div className="flex items-center justify-start gap-3 bg-gray-100 p-2">
        <button
          type="button"
          className="h-10 w-24 rounded !bg-blue-500 px-3 py-1 text-white"
          onClick={handlePrevWeek}
        >
          上一週
        </button>
        <button
          type="button"
          className="h-10 w-24 rounded !bg-gray-500 px-3 py-1 text-white"
          onClick={handleCurrentWeek}
        >
          本週
        </button>
        <button
          type="button"
          className="h-10 w-24 rounded !bg-blue-500 px-3 py-1 text-white"
          onClick={handleNextWeek}
        >
          下一週
        </button>
      </div>

      {/* 日期選擇器區域 */}
      <div className="flex items-center justify-between bg-gray-100 p-2">
        <DateRangePicker
          value={datePickerValue}
          onChange={handleTimeChange}
          disabled={currentView === Views.DAY}
          picker="date"
          format="YYYY/MM/DD"
        />
      </div>
    </>
  );
};
