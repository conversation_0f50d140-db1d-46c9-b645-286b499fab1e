import 'dayjs/locale/zh-tw';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { useState } from 'react';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.locale('zh-tw');

// 這些接口保留作為類型引用，但變數聲明移至組件內部
interface ShiftType {
  id: string;
  title: string;
  color: string;
}
interface Employee {
  id: string;
  name: string;
}
interface ShiftItem {
  id: string;
  employeeId: string;
  date: string; // 格式：YYYY-MM-DD
  shiftTypeId: string;
}

// 排班類型數據
const shiftTypes: ShiftType[] = [
  { id: 'early', title: '早班', color: '#4F46E5' },
  { id: 'middle', title: '中班', color: '#0EA5E9' },
  { id: 'late', title: '晚班', color: '#10B981' },
  { id: 'rest', title: '休', color: '#F59E0B' },
  { id: 'holiday', title: '休假日', color: '#6366F1' },
  { id: 'example', title: '例假日', color: '#EC4899' },
  { id: 'fixed', title: '固定日', color: '#8B5CF6' },
];

// 員工數據
const employees: Employee[] = [
  { id: '1', name: '王小明' },
  { id: '2', name: '李大仁' },
  { id: '3', name: '陳佐承' },
  { id: '4', name: '張美美' },
  { id: '5', name: '林志豪' },
  { id: '6', name: '黃雅芳' },
  { id: '7', name: '吳俊傑' },
  { id: '8', name: '楊詩涵' },
  { id: '9', name: '趙宇恆' },
  { id: '10', name: '許雅婷' },
  { id: '11', name: '周家豪' },
  { id: '12', name: '郭芳瑜' },
  { id: '13', name: '陳建志' },
  { id: '14', name: '蔡宜臻' },
  { id: '15', name: '蕭志偉' },
  { id: '16', name: '沈佳穎' },
  { id: '17', name: '劉俊宏' },
  { id: '18', name: '鄭雅文' },
  { id: '19', name: '何志明' },
  { id: '20', name: '謝雅雯' },
];

/**
 * 日曆表頭元件
 * 顯示日期與星期
 */
interface CalendarHeaderProps {
  days: string[]; // 日期陣列
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({ days }) => {
  // 星期對應表
  const weekdayMap = {
    0: '日',
    1: '一',
    2: '二',
    3: '三',
    4: '四',
    5: '五',
    6: '六',
  };

  return (
    <div className="sticky top-0 z-20 flex bg-gray-100" role="row" aria-rowindex={1}>
      <div
        className="sticky left-0 z-30 min-w-[100px] border border-gray-300 bg-gray-100 p-2 font-medium"
        role="columnheader"
      >
        員工
      </div>
      {days.map((day, index) => {
        const dayObj = dayjs(day);
        const isWeekend = dayObj.day() === 0 || dayObj.day() === 6;

        return (
          <div
            key={day}
            className={`min-w-[80px] flex-1 border border-gray-300 p-1 text-center ${isWeekend ? 'bg-gray-200' : ''}`}
            role="columnheader"
            aria-colindex={index + 2}
            data-date={day}
          >
            <div>{dayObj.date()}</div>
            <div className="text-xs text-gray-500">
              週{weekdayMap[dayObj.day() as keyof typeof weekdayMap]}
            </div>
          </div>
        );
      })}
    </div>
  );
};

/**
 * 日期單元格元件
 */
interface DayCellProps {
  day: string;
  employee: Employee;
}

const DayCell: React.FC<DayCellProps> = ({ day, employee }) => {
  const dayObj = dayjs(day);
  const isWeekend = dayObj.day() === 0 || dayObj.day() === 6;

  // 模擬的班次數據 (實際應用中可能從 API 獲取)
  const shifts: ShiftItem[] = [];

  const shift = shifts.find(s => s.employeeId === employee.id && s.date === day);
  const shiftType = shift ? shiftTypes.find(t => t.id === shift.shiftTypeId) : null;

  return (
    <div
      className={`min-w-[80px] flex-1 border border-gray-300 p-1 ${isWeekend ? 'bg-gray-100' : ''}`}
      data-employee-id={employee.id}
      data-date={day}
    >
      {shiftType && (
        <div
          className="rounded px-1 py-0.5 text-center text-xs text-white"
          style={{ backgroundColor: shiftType.color }}
        >
          {shiftType.title}
        </div>
      )}
    </div>
  );
};

/**
 * 員工行元件
 */
interface EmployeeRowProps {
  employee: Employee;
  days: string[];
  rowIndex: number;
}

const EmployeeRow: React.FC<EmployeeRowProps> = ({ employee, days, rowIndex }) => {
  return (
    <div className="flex" role="row" aria-rowindex={rowIndex + 2}>
      <div className="sticky left-0 z-10 flex min-w-[100px] items-center border border-gray-300 bg-white p-2">
        {employee.name}
      </div>
      {days.map(day => (
        <DayCell key={day} day={day} employee={employee} />
      ))}
    </div>
  );
};

/**
 * 日曆內容元件
 * 顯示員工和班次信息
 */
interface CalendarBodyProps {
  days: string[];
}

const CalendarBody: React.FC<CalendarBodyProps> = ({ days }) => {
  return (
    <div className="flex-1">
      {employees.map((employee, rowIndex) => (
        <EmployeeRow key={employee.id} employee={employee} days={days} rowIndex={rowIndex} />
      ))}
    </div>
  );
};

const ShiftSchedulerV2 = () => {
  const [currentMonth] = useState(dayjs());

  // 生成當月的所有日期
  const getDaysInMonth = () => {
    const daysInMonth = currentMonth.daysInMonth();
    const days = [];
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(currentMonth.date(i).format('YYYY-MM-DD'));
    }
    return days;
  };

  const days = getDaysInMonth();
  console.log('render');

  return (
    <div>
      {/* 日曆主體 - 使用 Flexbox */}
      <div className="transform-3d max-h-[600px] flex-1 overflow-auto">
        <div className="flex min-w-fit flex-col" role="grid">
          {/* 使用表頭元件 */}
          <CalendarHeader days={days} />

          {/* 使用內容元件 */}
          <CalendarBody days={days} />
        </div>
      </div>
    </div>
  );
};

export default ShiftSchedulerV2;
