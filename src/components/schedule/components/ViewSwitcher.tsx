import clsx from 'clsx';
import React, { useCallback } from 'react';
import { View, Views } from 'react-big-calendar';

import { CustomViews } from '../types';

interface ViewSwitcherProps {
  view: View | CustomViews;
  onViewChange: (view: View | CustomViews) => void;
}

export const ViewSwitcher: React.FC<ViewSwitcherProps> = React.memo(({ view, onViewChange }) => {
  const handleViewChange = useCallback(
    (newView: View | CustomViews) => () => {
      onViewChange(newView);
    },
    [onViewChange],
  );

  return (
    <div className="mb-4 ml-4 mt-4 flex gap-2">
      <button
        type="button"
        className={clsx('rounded-md px-4 py-2 text-sm', {
          '!bg-blue-500 text-white': view === Views.DAY,
          '!bg-gray-100 text-gray-700': view !== Views.DAY,
        })}
        onClick={handleViewChange(Views.DAY)}
      >
        日
      </button>
      <button
        type="button"
        className={clsx('rounded-md px-4 py-2 text-sm', {
          '!bg-blue-500 text-white': view === Views.WEEK,
          '!bg-gray-100 text-gray-700': view !== Views.WEEK,
        })}
        onClick={handleViewChange(Views.WEEK)}
      >
        週
      </button>
      <button
        type="button"
        className={clsx('rounded-md px-4 py-2 text-sm', {
          '!bg-blue-500 text-white': view === Views.MONTH,
          '!bg-gray-100 text-gray-700': view !== Views.MONTH,
        })}
        onClick={handleViewChange(Views.MONTH)}
      >
        月
      </button>
      <button
        type="button"
        className={clsx('rounded-md px-4 py-2 text-sm', {
          '!bg-blue-500 text-white': view === CustomViews.BIWEEK,
          '!bg-gray-100 text-gray-700': view !== CustomViews.BIWEEK,
        })}
        onClick={handleViewChange(CustomViews.BIWEEK)}
      >
        雙週
      </button>
      <button
        type="button"
        className={clsx('rounded-md px-4 py-2 text-sm', {
          '!bg-blue-500 text-white': view === CustomViews.FOURWEEK,
          '!bg-gray-100 text-gray-700': view !== CustomViews.FOURWEEK,
        })}
        onClick={handleViewChange(CustomViews.FOURWEEK)}
      >
        四週
      </button>
    </div>
  );
});
