import dayjs from 'dayjs';

import { TimelineItem } from '../types';

// 定義類型
interface Employee {
  id: string;
  title: string;
  bgColor: string;
}

interface Store {
  id: string;
  name: string;
  location: string;
}

interface ShiftType {
  title: string;
  start: number;
  end: number;
  color: string;
}

// 定義10位員工（使用英文姓名）
export const timelineGroups: Employee[] = [
  { id: '1', title: '<PERSON>', bgColor: '#4F46E5' },
  { id: '2', title: '<PERSON>', bgColor: '#0EA5E9' },
  { id: '3', title: '<PERSON>', bgColor: '#10B981' },
  { id: '4', title: '<PERSON>', bgColor: '#F59E0B' },
  { id: '5', title: '<PERSON>', bgColor: '#6366F1' },
  { id: '6', title: '<PERSON>', bgColor: '#EC4899' },
  { id: '7', title: '<PERSON>', bgColor: '#8B5CF6' },
  { id: '8', title: '<PERSON>', bgColor: '#14B8A6' },
  { id: '9', title: '<PERSON>', bgColor: '#F97316' },
  { id: '10', title: '<PERSON> Taylor', bgColor: '#EF4444' },
];

// 台北市10間門店
export const stores: Store[] = [
  { id: 'store_1', name: '信義旗艦店', location: '台北市信義區信義路五段' },
  { id: 'store_2', name: '東區門市', location: '台北市大安區忠孝東路四段' },
  { id: 'store_3', name: '西門門市', location: '台北市萬華區成都路' },
  { id: 'store_4', name: '南京店', location: '台北市中山區南京東路' },
  { id: 'store_5', name: '北投溫泉店', location: '台北市北投區光明路' },
  { id: 'store_6', name: '內湖科技店', location: '台北市內湖區內湖路一段' },
  { id: 'store_7', name: '松山車站店', location: '台北市松山區八德路四段' },
  { id: 'store_8', name: '南港展覽店', location: '台北市南港區經貿二路' },
  { id: 'store_9', name: '天母商圈店', location: '台北市士林區天母西路' },
  { id: 'store_10', name: '台大公館店', location: '台北市大安區羅斯福路四段' },
];

// 班次類型
const shiftTypes: ShiftType[] = [
  { title: '早班', start: 9, end: 14, color: '#4F46E5' },
  { title: '午班', start: 11, end: 16, color: '#0EA5E9' },
  { title: '晚班', start: 17, end: 23, color: '#10B981' },
  { title: '支援班', start: 14, end: 19, color: '#F59E0B' },
  { title: '夜班', start: 19, end: 24, color: '#6366F1' },
];

// 生成4月1-4月3日的班次資料
export const timelineItems: TimelineItem[] = generateTimelineItems();

function generateTimelineItems(): TimelineItem[] {
  const items: TimelineItem[] = [];
  let itemId = 1;

  // 獲取今天的日期
  const today = dayjs();

  // 生成今天以及前後各一天的班表 (昨天、今天、明天)
  for (let dayOffset = -1; dayOffset <= 1; dayOffset++) {
    const date = today.add(dayOffset, 'day');

    // 為每個員工安排1-2個班次
    timelineGroups.forEach(employee => {
      // 決定這天排幾個班次 (1-2個)
      const shiftsCount = Math.floor(Math.random() * 2) + 1;

      for (let i = 0; i < shiftsCount; i++) {
        // 隨機選擇班次類型
        const shiftType = shiftTypes[Math.floor(Math.random() * shiftTypes.length)];
        // 隨機選擇門店
        const store = stores[Math.floor(Math.random() * stores.length)];

        // 確保同一員工在同一天的班次不重疊
        let startHour = shiftType.start;
        let endHour = shiftType.end;

        // 如果是第二個班次，調整時間避免重疊
        if (i === 1) {
          // 找到這個員工在這天已有的班次
          const existingShift = items.find(
            item =>
              item.group === employee.id &&
              dayjs(item.start_time).format('YYYY-MM-DD') === date.format('YYYY-MM-DD'),
          );

          if (existingShift) {
            const existingEndHour = dayjs(existingShift.end_time).hour();
            // 如果現有班次結束時間在下午，安排晚上的班次
            if (existingEndHour < 17) {
              startHour = 18;
              endHour = 23;
            } else {
              // 如果已有晚班，安排早班
              startHour = 9;
              endHour = 14;
            }
          }
        }

        items.push({
          id: String(itemId++),
          group: employee.id,
          title: `${shiftType.title}(${String(startHour).padStart(2, '0')}:00-${String(endHour).padStart(2, '0')}:00)`,
          start_time: date.hour(startHour).minute(0).valueOf(),
          end_time: date.hour(endHour).minute(0).valueOf(),
          storeId: store.id,
          store: store.name,
          location: store.location,
          itemProps: {
            style: { backgroundColor: shiftType.color, color: 'white' },
          },
        });
      }
    });
  }

  return items;
}
