import 'react-calendar-timeline/dist/style.css';
import '../timeline.css'; // 導入自定義時間軸樣式

import { TimelineGroup } from '../types';
import { TimelineContent } from './TimelineContent';
import { TimelineControls } from './TimelineControls';
import { timelineGroups } from './TimelineData';

const TimelineDemo = () => (
  <div className="flex h-full w-full flex-col">
    {/* 控制面板組件 */}
    <TimelineControls />
    {/* 時間軸內容組件 */}
    <div className="w-full flex-1 overflow-hidden">
      <TimelineContent timelineGroups={timelineGroups as TimelineGroup[]} />
    </div>
  </div>
);

export default TimelineDemo;
