import React from 'react';

import { Employee } from './atoms/employeeAtoms';

export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  location?: string;
  resourceId?: string;
  employeeId?: string;
}

export interface CalendarProps {
  events?: CalendarEvent[];
  onEventChange?: (event: CalendarEvent) => void;
  onEventCreate?: (event: CalendarEvent) => void;
  onEventDelete?: (eventId: string) => void;
}

export interface EventManagementOptions {
  onEventChange?: (event: CalendarEvent) => void;
  onEventCreate?: (event: CalendarEvent) => void;
  onEventDelete?: (eventId: string) => void;
}

export interface EventType {
  id: string;
  title: string;
  start: Date;
  end: Date;
  allDay?: boolean;
  location: string;
  employeeId?: string;
  resourceId?: string;
}

export enum CustomViews {
  BIWEEK = 'biweek', // 雙週
  FOURWEEK = 'fourweek', // 四週
  EIGHTWEEK = 'eightweek', // 八週
}

// 時間線元件相關類型
export interface TimelineItem {
  id: string;
  group: string;
  title: string;
  start_time: number | Date;
  end_time: number | Date;
  itemProps?: {
    style?: React.CSSProperties;
    className?: string;
  };
  location?: string;
  storeId?: string;
  store?: string;
  [key: string]: unknown;
}

export interface TimelineGroup {
  id: string;
  title: string;
  rightTitle?: string;
  bgColor?: string;
  selected?: boolean;
  [key: string]: unknown;
}

// 時間線項目渲染器屬性
export interface ItemRendererProps {
  item: TimelineItem;
  itemContext: {
    selected: boolean;
    dragging: boolean;
    dimensions: {
      width: number;
      height: number;
    };
  };
  getItemProps: (props?: Record<string, unknown>) => Record<string, unknown>;
  getResizeProps?: () => {
    left: unknown;
    right: unknown;
  };
  timelineContext: unknown;
}

// 時間線群組渲染器屬性
export interface GroupRendererProps {
  group: TimelineGroup;
}

// 側邊欄頭部渲染器屬性
export interface SidebarHeaderProps {
  getRootProps: () => React.HTMLProps<HTMLDivElement>;
}

// 時間步長設定
export interface TimeSteps {
  second: number;
  minute: number;
  hour: number;
  day: number;
  month: number;
  year: number;
}

// 日視圖拖放事件參數
export interface EventDropArgs {
  event: {
    id: string;
    start: Date;
    end: Date;
    employeeId?: string;
  };
  start: Date;
  end: Date;
  resourceId: string;
}

// 日視圖調整大小事件參數
export interface EventResizeArgs {
  event: {
    id: string;
    start: Date;
    end: Date;
  };
  start: Date;
  end: Date;
}

// 日視圖選擇槽參數
export interface SelectSlotArgs {
  start: Date;
  end: Date;
  resourceId: string;
}

// 日視圖時間線元件屬性
export interface DayResourceTimelineProps {
  events: EventType[];
  employees: Employee[];
  date: Date;
  onEventDrop: (args: EventDropArgs) => void;
  onEventResize: (args: EventResizeArgs) => void;
  onSelectSlot: (slotInfo: SelectSlotArgs) => void;
}
