import dayjs from 'dayjs';
import { atom } from 'jotai';
import { atomWithImmer } from 'jotai-immer';

import { stores, timelineItems as initialTimelineItems } from '../components/TimelineData';
import { TimelineGroup, TimelineItem } from '../types';

/**
 * 使用atomWithImmer创建时间线项目原子
 * 这允许我们直接使用可变风格的语法来更新状态
 */
export const timelineItemsAtom = atomWithImmer<TimelineItem[]>(initialTimelineItems);

/**
 * 存储所有员工组的原子
 */
export const groupsItemsAtom = atomWithImmer<TimelineGroup[]>([]);

/**
 * 员工筛选状态原子
 * null 表示显示所有员工
 */
export const selectedEmployeeAtom = atom<string[]>([]);

/**
 * 門店篩選狀態原子
 * 空陣列表示顯示所有門店
 */
export const selectedStoresAtom = atom<string[]>([]);

/**
 * 獲取所有可用門店的原子
 */
export const availableStoresAtom = atom<string[]>(() => {
  // 直接使用 TimelineData 中定義的門店列表
  return stores.map(store => store.id);
});

/**
 * 更新员工选择并同步群组选中状态的原子
 */
export const updateSelectedEmployeeAtom = atom(null, (_get, set, newSelectedIds: string[]) => {
  set(selectedEmployeeAtom, newSelectedIds);

  // 更新群組的選中狀態
  set(groupsItemsAtom, draft => {
    draft.forEach(group => {
      // 設置 group 的 selected 屬性
      if (group.selected !== undefined) {
        group.selected = newSelectedIds.includes(group.id.toString());
      }
    });
  });
});

/**
 * 更新選中門店的原子
 */
export const updateSelectedStoresAtom = atom(null, (_get, set, selectedStores: string[]) => {
  set(selectedStoresAtom, selectedStores);
});

/**
 * 根据筛选条件过滤后的时间线项目
 */
export const filteredTimelineItemsAtom = atom<TimelineItem[]>(get => {
  const allItems = get(timelineItemsAtom);
  const selectedEmployees = get(selectedEmployeeAtom);
  const selectedStoreIds = get(selectedStoresAtom);

  return allItems.filter(item => {
    // 如果沒有選擇任何員工，則顯示所有員工的項目
    const passEmployeeFilter =
      selectedEmployees.length === 0 || selectedEmployees.includes(item.group);

    // 獲取門店 ID
    const storeId = item.storeId || findStoreIdByName(item.store);

    // 如果沒有選擇任何門店，則顯示所有門店的項目
    const passStoreFilter =
      selectedStoreIds.length === 0 || (storeId && selectedStoreIds.includes(storeId));

    // 同時滿足員工和門店的篩選條件
    return passEmployeeFilter && passStoreFilter;
  });
});

/**
 * 设置员工列表的原子
 */
export const setGroupsAtom = atom(null, (_get, set, groups: TimelineGroup[]) => {
  set(groupsItemsAtom, groups);
});

/**
 * 移动项目的Action Atom
 *
 * 使用jotai-immer实现，允许直接修改状态
 *
 * 使用方式：
 * const moveItem = useSetAtom(moveTimelineItemAtom);
 * moveItem({ itemId, dragTime, newGroupOrder, groups });
 */
export const moveTimelineItemAtom = atom(
  null,
  (
    get,
    set,
    {
      itemId,
      dragTime,
      newGroupOrder,
      groups,
    }: {
      itemId: string | number; // 要移动的项目ID
      dragTime: number; // 新的时间点
      newGroupOrder: number; // 新的组索引
      groups: TimelineGroup[]; // 组列表
    },
  ) => {
    // 根据组索引获取实际的组ID
    const newGroupId = groups[newGroupOrder]?.id;
    if (!newGroupId) {
      console.error('找不到目标组:', newGroupOrder);
      return null; // 如果找不到组，则终止操作
    }

    // 使用atomWithImmer直接更新状态
    set(timelineItemsAtom, draft => {
      const item = draft.find(item => item.id === itemId.toString());
      if (!item) {
        console.warn('未找到要移动的项目:', itemId);
        return;
      }

      // 计算时间差值
      const timeDiff = dragTime - (item.start_time as number);

      // 直接修改draft对象，atomWithImmer会处理不可变性
      item.group = newGroupId.toString();
      item.start_time = (item.start_time as number) + timeDiff;
      item.end_time = (item.end_time as number) + timeDiff;
    });

    // 记录操作
    const items = get(timelineItemsAtom);
    const item = items.find(item => item.id === itemId.toString());
    if (item) {
      console.log('项目已移动:', {
        itemId,
        newGroupId,
        newStartTime: dayjs(item.start_time as number).format('YYYY-MM-DD HH:mm'),
        newEndTime: dayjs(item.end_time as number).format('YYYY-MM-DD HH:mm'),
      });
    }

    return item;
  },
);

/**
 * 调整项目大小的Action Atom
 *
 * 使用jotai-immer实现，允许直接修改状态
 *
 * 使用方式：
 * const resizeItem = useSetAtom(resizeTimelineItemAtom);
 * resizeItem({ itemId, time, edge });
 */
export const resizeTimelineItemAtom = atom(
  null,
  (
    get,
    set,
    {
      itemId,
      time,
      edge,
    }: {
      itemId: string | number; // 要调整大小的项目ID
      time: number; // 调整后的时间点
      edge: 'left' | 'right'; // 调整哪一边 (左侧=开始时间, 右侧=结束时间)
    },
  ) => {
    // 使用atomWithImmer直接更新状态
    set(timelineItemsAtom, draft => {
      const item = draft.find(item => item.id === itemId.toString());
      if (!item) {
        console.warn('未找到要调整大小的项目:', itemId);
        return;
      }

      // 根据调整的边缘更新开始或结束时间
      if (edge === 'left') {
        // 确保开始时间不晚于结束时间
        if (time < (item.end_time as number)) {
          item.start_time = time;
        } else {
          console.warn('调整无效: 开始时间不能晚于结束时间');
        }
      } else if (edge === 'right') {
        // 确保结束时间不早于开始时间
        if (time > (item.start_time as number)) {
          item.end_time = time;
        } else {
          console.warn('调整无效: 结束时间不能早于开始时间');
        }
      }
    });

    // 记录操作
    const items = get(timelineItemsAtom);
    const item = items.find(item => item.id === itemId.toString());
    if (item) {
      console.log('项目大小已调整:', {
        itemId,
        edge,
        time: dayjs(time).format('YYYY-MM-DD HH:mm'),
        newStartTime: dayjs(item.start_time as number).format('YYYY-MM-DD HH:mm'),
        newEndTime: dayjs(item.end_time as number).format('YYYY-MM-DD HH:mm'),
      });
    }

    // 返回调整后的项目
    return item;
  },
);

// 輔助函數：根據門店名稱查找門店 ID
function findStoreIdByName(storeName?: string): string | undefined {
  if (!storeName) return undefined;

  const store = stores.find(s => s.name === storeName);
  return store?.id;
}
