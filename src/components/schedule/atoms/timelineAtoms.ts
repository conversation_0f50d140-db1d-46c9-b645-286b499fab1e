import dayjs from 'dayjs';
import { atom } from 'jotai';
import { View, Views } from 'react-big-calendar';

import { CustomViews } from '../types';

// 獲取本週的開始和結束時間
const getWeekRange = () => {
  const now = dayjs();
  const startOfWeek = now.startOf('week');
  return {
    start: startOfWeek.valueOf(),
    end: startOfWeek.add(7, 'day').valueOf(),
  };
};

const { start: defaultTimeStart, end: defaultTimeEnd } = getWeekRange();

// 定義時間範圍原子
export const timeRangeAtom = atom({
  start: defaultTimeStart,
  end: defaultTimeEnd,
});

// 定義當前視圖原子
export const currentViewAtom = atom<View | CustomViews>(Views.WEEK);

// 定義時間步進原子
export const timeStepsAtom = atom({
  second: 0,
  minute: 0,
  hour: 12,
  day: 1,
  month: 1,
  year: 1,
});

// 週操作原子
export const weekActionsAtom = atom(null, (get, set, action: 'prev' | 'next' | 'current') => {
  const currentRange = get(timeRangeAtom);

  switch (action) {
    case 'prev':
      set(timeRangeAtom, {
        start: dayjs(currentRange.start).subtract(7, 'day').valueOf(),
        end: dayjs(currentRange.end).subtract(7, 'day').valueOf(),
      });
      break;
    case 'next':
      set(timeRangeAtom, {
        start: dayjs(currentRange.start).add(7, 'day').valueOf(),
        end: dayjs(currentRange.end).add(7, 'day').valueOf(),
      });
      break;
    case 'current':
      set(timeRangeAtom, {
        start: defaultTimeStart,
        end: defaultTimeEnd,
      });
      break;
    default:
      // 如果傳入未定義的操作，預設跳至當前週
      set(timeRangeAtom, {
        start: defaultTimeStart,
        end: defaultTimeEnd,
      });
      break;
  }
});

// 時間變化原子
export const timeChangeAtom = atom(
  null,
  (_get, set, update: { visibleTimeStart: number; visibleTimeEnd: number }) => {
    set(timeRangeAtom, {
      start: update.visibleTimeStart,
      end: update.visibleTimeEnd,
    });
  },
);

// 派生原子：根據視圖變化更新時間步進
export const viewDependentTimeStepsAtom = atom(
  get => get(timeStepsAtom),
  (_get, set, view: View | CustomViews) => {
    // 根據不同視圖類型調整時間範圍
    const now = dayjs();
    const startOfWeek = now.startOf('week');

    switch (view) {
      case Views.DAY:
        set(timeRangeAtom, {
          start: now.startOf('day').valueOf(),
          end: now.endOf('day').valueOf(),
        });
        // 日視圖時設置為一小時一個刻度
        set(timeStepsAtom, {
          second: 0,
          minute: 0,
          hour: 1, // 一小時一個刻度
          day: 1,
          month: 1,
          year: 1,
        });
        break;
      case Views.WEEK:
        set(timeRangeAtom, {
          start: startOfWeek.valueOf(),
          end: startOfWeek.add(7, 'day').valueOf(),
        });
        // 週視圖時設置為半天一個刻度
        set(timeStepsAtom, {
          second: 0,
          minute: 0,
          hour: 12, // 12小時一個刻度
          day: 1,
          month: 1,
          year: 1,
        });
        break;
      case Views.MONTH:
        set(timeRangeAtom, {
          start: now.startOf('month').valueOf(),
          end: now.endOf('month').valueOf(),
        });
        break;
      case CustomViews.BIWEEK:
        set(timeRangeAtom, {
          start: startOfWeek.valueOf(),
          end: startOfWeek.add(14, 'day').valueOf(),
        });
        break;
      case CustomViews.FOURWEEK:
        set(timeRangeAtom, {
          start: startOfWeek.valueOf(),
          end: startOfWeek.add(28, 'day').valueOf(),
        });
        break;
      default:
        set(timeRangeAtom, {
          start: startOfWeek.valueOf(),
          end: startOfWeek.add(7, 'day').valueOf(),
        });
    }

    set(currentViewAtom, view);
  },
);
