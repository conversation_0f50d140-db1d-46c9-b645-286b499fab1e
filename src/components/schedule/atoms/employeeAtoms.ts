import { atom } from 'jotai';

// 员工接口定义
export interface Employee {
  id: string;
  name: string;
  department: string;
  position: string;
  color: string; // 班表颜色
  isSelected: boolean; // 是否被选中显示
}

// 模拟员工数据
const mockEmployees: Employee[] = [
  {
    id: 'emp-001',
    name: '陳小明',
    department: '銷售部',
    position: '銷售專員',
    color: '#4F46E5', // indigo-600
    isSelected: true,
  },
  {
    id: 'emp-002',
    name: '林美玲',
    department: '銷售部',
    position: '銷售主管',
    color: '#7C3AED', // purple-600
    isSelected: true,
  },
  {
    id: 'emp-003',
    name: '王大同',
    department: '客服部',
    position: '客服專員',
    color: '#059669', // emerald-600
    isSelected: true,
  },
  {
    id: 'emp-004',
    name: '李小華',
    department: '客服部',
    position: '客服專員',
    color: '#DC2626', // red-600
    isSelected: true,
  },
  {
    id: 'emp-005',
    name: '張雅婷',
    department: '人資部',
    position: '人資專員',
    color: '#0284C7', // sky-600
    isSelected: true,
  },
];

// 员工列表原子
export const employeesAtom = atom<Employee[]>(mockEmployees);

// 选中员工原子
export const selectedEmployeesAtom = atom(get => get(employeesAtom).filter(emp => emp.isSelected));

// 更新员工选择状态
export const toggleEmployeeSelectionAtom = atom(null, (get, set, employeeId: string) => {
  const employees = get(employeesAtom);
  const updatedEmployees = employees.map(emp =>
    emp.id === employeeId ? { ...emp, isSelected: !emp.isSelected } : emp,
  );
  set(employeesAtom, updatedEmployees);
});

// 全选/取消全选
export const toggleAllEmployeesAtom = atom(null, (get, set, selectAll: boolean) => {
  const employees = get(employeesAtom);
  const updatedEmployees = employees.map(emp => ({ ...emp, isSelected: selectAll }));
  set(employeesAtom, updatedEmployees);
});
