import { Lock } from 'lucide-react';
import { FC } from 'react';

/**
 * 未授權頁面組件
 * 在 iframe 模式下顯示的未登入頁面
 */
const UnauthorizedPage: FC = () => {
  return (
    <div className="flex h-screen w-full items-center justify-center bg-gray-50">
      <div className="text-center">
        {/* 圖案 */}
        <div className="mb-8 flex justify-center">
          <div className="flex h-24 w-24 items-center justify-center rounded-full bg-gray-200">
            <Lock className="h-12 w-12 text-gray-500" />
          </div>
        </div>

        {/* 標題 */}
        <h1 className="mb-4 text-2xl font-semibold text-gray-800">需要登入</h1>

        {/* 描述 */}
        <p className="mb-6 text-gray-600">您需要先登入才能訪問此頁面</p>

        {/* 提示 */}
        <p className="text-sm text-gray-500">請聯繫系統管理員或重新登入</p>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
