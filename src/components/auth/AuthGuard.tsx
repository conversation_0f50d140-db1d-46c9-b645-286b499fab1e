import { FC, PropsWithChildren, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { useIsEmbedded } from '@/hooks/useEmbedMode';
import { getLoginUrl, isUserLoggedIn } from '@/utils/auth';

import RedirectingPage from './RedirectingPage';
import UnauthorizedPage from './UnauthorizedPage';

/**
 * AuthGuard 組件
 * 處理登入驗證和重定向邏輯
 */
const AuthGuard: FC<PropsWithChildren> = ({ children }) => {
  const location = useLocation();
  const isEmbedded = useIsEmbedded();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(null);

  // 檢查登入狀態
  useEffect(() => {
    const checkLoginStatus = () => {
      const loggedIn = isUserLoggedIn();
      setIsLoggedIn(loggedIn);

      // 如果未登入且非 iframe 模式，開始重定向
      if (!loggedIn && !isEmbedded && !isRedirecting) {
        setIsRedirecting(true);
        const loginUrl = getLoginUrl();
        window.location.href = loginUrl;
      }
    };

    // 每次路由變化時檢查登入狀態
    checkLoginStatus();
  }, [location.pathname, isEmbedded, isRedirecting]);

  // 如果還在檢查登入狀態，顯示 loading
  if (isLoggedIn === null) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-gray-600">檢查登入狀態...</div>
        </div>
      </div>
    );
  }

  // 如果未登入且在 iframe 模式，顯示未授權頁面
  if (!isLoggedIn && isEmbedded) {
    return <UnauthorizedPage />;
  }

  // 如果正在重定向，顯示跳轉頁面
  if (isRedirecting) {
    return <RedirectingPage />;
  }

  // 如果已登入，正常渲染子組件
  if (isLoggedIn) {
    return <>{children}</>;
  }

  // 其他情況（未登入且非 iframe 模式），顯示跳轉頁面
  return <RedirectingPage />;
};

export default AuthGuard;
