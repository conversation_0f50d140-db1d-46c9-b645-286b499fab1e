import { Loader2 } from 'lucide-react';
import { FC } from 'react';

/**
 * 跳轉中頁面組件
 * 在重定向到登入頁面時顯示的 loading 頁面
 */
const RedirectingPage: FC = () => {
  return (
    <div className="flex h-screen w-full items-center justify-center bg-gray-50">
      <div className="text-center">
        {/* Loading 圖案 */}
        <div className="mb-6 flex justify-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
        </div>

        {/* 標題 */}
        <h1 className="mb-4 text-xl font-semibold text-gray-800">正在跳轉到登入頁面...</h1>

        {/* 描述 */}
        <p className="text-gray-600">請稍候，即將為您跳轉到登入頁面</p>
      </div>
    </div>
  );
};

export default RedirectingPage;
