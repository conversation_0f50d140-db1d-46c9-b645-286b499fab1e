// 錯誤處理組件統一導出

export { default as ErrorBoundary } from './ErrorBoundary';
export { default as ErrorNotifications } from './ErrorNotifications';
export type { ErrorPageProps } from './ErrorPage';
export { default as ErrorPage } from './ErrorPage';
export { default as NetworkErrorPage } from './NetworkErrorPage';
export { default as NotFoundPage } from './NotFoundPage';
export { default as RouteErrorBoundary } from './RouteErrorBoundary';
export { default as ServerErrorPage } from './ServerErrorPage';

// 重新導出現有的認證錯誤頁面
export { default as UnauthorizedPage } from '../auth/UnauthorizedPage';
