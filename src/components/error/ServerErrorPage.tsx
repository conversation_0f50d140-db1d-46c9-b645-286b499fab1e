import { Server } from 'lucide-react';
import { FC } from 'react';

import ErrorPage from './ErrorPage';

interface ServerErrorPageProps {
  /** 錯誤代碼 */
  errorCode?: number;
  /** 自定義重試函數 */
  onRetry?: () => void;
}

/**
 * 服務器錯誤頁面組件
 * 支援 500, 502, 503, 504 等服務器錯誤
 */
const ServerErrorPage: FC<ServerErrorPageProps> = ({ errorCode = 500, onRetry }) => {
  const getErrorMessage = (code: number) => {
    switch (code) {
      case 500:
        return '服務器內部錯誤，請稍後再試';
      case 502:
        return '網關錯誤，服務暫時不可用';
      case 503:
        return '服務暫時不可用，請稍後再試';
      case 504:
        return '網關超時，請重新嘗試';
      default:
        return '服務器發生錯誤，請稍後再試';
    }
  };

  return (
    <ErrorPage
      type="server"
      errorCode={errorCode}
      title="服務器錯誤"
      description={getErrorMessage(errorCode)}
      icon={<Server className="h-16 w-16 text-orange-500" />}
      showRetry={true}
      showHome={true}
      onRetry={onRetry}
    />
  );
};

export default ServerErrorPage;
