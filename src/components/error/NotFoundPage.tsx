import { Search } from 'lucide-react';
import { FC } from 'react';

import ErrorPage from './ErrorPage';

/**
 * 404 頁面未找到組件
 */
const NotFoundPage: FC = () => {
  return (
    <ErrorPage
      type="notFound"
      errorCode="404"
      title="頁面未找到"
      description="抱歉，您訪問的頁面不存在或已被移除"
      icon={<Search className="h-16 w-16 text-blue-500" />}
      showRetry={false}
      showHome={true}
    />
  );
};

export default NotFoundPage;
