import { WifiOff } from 'lucide-react';
import { FC } from 'react';

import ErrorPage from './ErrorPage';

interface NetworkErrorPageProps {
  /** 自定義重試函數 */
  onRetry?: () => void;
}

/**
 * 網路錯誤頁面組件
 * 處理網路連接失敗、超時等問題
 */
const NetworkErrorPage: FC<NetworkErrorPageProps> = ({ onRetry }) => {
  return (
    <ErrorPage
      type="network"
      title="網路連接失敗"
      description="無法連接到服務器，請檢查您的網路連接"
      icon={<WifiOff className="h-16 w-16 text-red-500" />}
      showRetry={true}
      showHome={true}
      onRetry={onRetry}
    />
  );
};

export default NetworkErrorPage;
