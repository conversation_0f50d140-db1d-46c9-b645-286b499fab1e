import { FC } from 'react';
import { isRouteErrorResponse, useRouteError } from 'react-router-dom';

import { ErrorPage, NotFoundPage, ServerErrorPage } from './index';

/**
 * 路由錯誤邊界組件
 * 處理 React Router 的路由級別錯誤
 */
const RouteErrorBoundary: FC = () => {
  const error = useRouteError();

  console.error('路由錯誤:', error);

  // 處理 React Router 的錯誤響應
  if (isRouteErrorResponse(error)) {
    switch (error.status) {
      case 404:
        return <NotFoundPage />;
      case 401:
        return (
          <ErrorPage
            type="unauthorized"
            errorCode={401}
            title="未授權訪問"
            description="您需要登入才能訪問此頁面"
            showRetry={false}
            showHome={true}
          />
        );
      case 403:
        return (
          <ErrorPage
            type="forbidden"
            errorCode={403}
            title="權限不足"
            description="您沒有權限訪問此頁面"
            showRetry={false}
            showHome={true}
          />
        );
      case 500:
      case 502:
      case 503:
      case 504:
        return <ServerErrorPage errorCode={error.status} />;
      default:
        return (
          <ErrorPage
            type="generic"
            errorCode={error.status}
            title={`HTTP ${error.status}`}
            description={error.statusText || '發生未知錯誤'}
            showRetry={true}
            showHome={true}
          />
        );
    }
  }

  // 處理其他類型的錯誤
  if (error instanceof Error) {
    // 檢查是否為代碼分割載入錯誤
    const isChunkLoadError =
      error.name === 'ChunkLoadError' ||
      error.message?.includes('Loading chunk') ||
      error.message?.includes('Loading CSS chunk');

    if (isChunkLoadError) {
      return (
        <ErrorPage
          type="generic"
          title="應用程式更新"
          description="應用程式已更新，請重新載入頁面"
          showRetry={true}
          showHome={false}
          onRetry={() => window.location.reload()}
          actions={
            <button
              onClick={() => window.location.reload()}
              className="mt-4 rounded-lg bg-blue-600 px-6 py-2 text-white hover:bg-blue-700"
            >
              重新載入頁面
            </button>
          }
        />
      );
    }

    // 其他 JavaScript 錯誤
    return (
      <ErrorPage
        type="generic"
        title="頁面載入失敗"
        description={error.message || '頁面載入時發生錯誤'}
        showRetry={true}
        showHome={true}
        onRetry={() => window.location.reload()}
      />
    );
  }

  // 未知錯誤類型
  return (
    <ErrorPage
      type="generic"
      title="發生未知錯誤"
      description="頁面載入時發生未知錯誤，請重試"
      showRetry={true}
      showHome={true}
      onRetry={() => window.location.reload()}
    />
  );
};

export default RouteErrorBoundary;
