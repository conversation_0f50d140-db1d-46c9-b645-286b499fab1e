import { AlertTriangle, Home, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { FC, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';

export interface ErrorPageProps {
  /** 錯誤類型 */
  type: 'network' | 'server' | 'notFound' | 'forbidden' | 'unauthorized' | 'timeout' | 'generic';
  /** 錯誤標題 */
  title?: string;
  /** 錯誤描述 */
  description?: string;
  /** 錯誤代碼 */
  errorCode?: string | number;
  /** 是否顯示重試按鈕 */
  showRetry?: boolean;
  /** 是否顯示返回首頁按鈕 */
  showHome?: boolean;
  /** 自定義重試函數 */
  onRetry?: () => void;
  /** 自定義圖標 */
  icon?: ReactNode;
  /** 額外的操作按鈕 */
  actions?: ReactNode;
}

/**
 * 統一錯誤頁面組件
 * 支援多種錯誤類型的顯示和處理
 */
const ErrorPage: FC<ErrorPageProps> = ({
  type,
  title,
  description,
  errorCode,
  showRetry = true,
  showHome = true,
  onRetry,
  icon,
  actions,
}) => {
  const navigate = useNavigate();

  // 根據錯誤類型獲取預設配置
  const getErrorConfig = () => {
    switch (type) {
      case 'network':
        return {
          defaultTitle: '網路連接失敗',
          defaultDescription: '請檢查您的網路連接並重試',
          defaultIcon: <WifiOff className="h-16 w-16 text-red-500" />,
          bgColor: 'bg-red-50',
          iconBgColor: 'bg-red-100',
        };
      case 'server':
        return {
          defaultTitle: '服務器錯誤',
          defaultDescription: '服務器暫時無法處理您的請求，請稍後再試',
          defaultIcon: <AlertTriangle className="h-16 w-16 text-orange-500" />,
          bgColor: 'bg-orange-50',
          iconBgColor: 'bg-orange-100',
        };
      case 'notFound':
        return {
          defaultTitle: '頁面未找到',
          defaultDescription: '您訪問的頁面不存在或已被移除',
          defaultIcon: <AlertTriangle className="h-16 w-16 text-blue-500" />,
          bgColor: 'bg-blue-50',
          iconBgColor: 'bg-blue-100',
        };
      case 'forbidden':
        return {
          defaultTitle: '權限不足',
          defaultDescription: '您沒有權限訪問此頁面',
          defaultIcon: <AlertTriangle className="h-16 w-16 text-yellow-500" />,
          bgColor: 'bg-yellow-50',
          iconBgColor: 'bg-yellow-100',
        };
      case 'unauthorized':
        return {
          defaultTitle: '未授權訪問',
          defaultDescription: '請重新登入後再試',
          defaultIcon: <AlertTriangle className="h-16 w-16 text-purple-500" />,
          bgColor: 'bg-purple-50',
          iconBgColor: 'bg-purple-100',
        };
      case 'timeout':
        return {
          defaultTitle: '請求超時',
          defaultDescription: '請求處理時間過長，請重試',
          defaultIcon: <Wifi className="h-16 w-16 text-indigo-500" />,
          bgColor: 'bg-indigo-50',
          iconBgColor: 'bg-indigo-100',
        };
      default:
        return {
          defaultTitle: '發生錯誤',
          defaultDescription: '系統發生未知錯誤，請聯繫管理員',
          defaultIcon: <AlertTriangle className="h-16 w-16 text-gray-500" />,
          bgColor: 'bg-gray-50',
          iconBgColor: 'bg-gray-100',
        };
    }
  };

  const config = getErrorConfig();

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      // 預設重試行為：重新載入頁面
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <div className={`flex h-screen w-full items-center justify-center ${config.bgColor}`}>
      <div className="max-w-md text-center">
        {/* 錯誤圖標 */}
        <div className="mb-8 flex justify-center">
          <div
            className={`flex h-24 w-24 items-center justify-center rounded-full ${config.iconBgColor}`}
          >
            {icon || config.defaultIcon}
          </div>
        </div>

        {/* 錯誤代碼 */}
        {errorCode && (
          <div className="mb-4">
            <span className="text-4xl font-bold text-gray-400">{errorCode}</span>
          </div>
        )}

        {/* 錯誤標題 */}
        <h1 className="mb-4 text-2xl font-semibold text-gray-800">
          {title || config.defaultTitle}
        </h1>

        {/* 錯誤描述 */}
        <p className="mb-8 text-gray-600">{description || config.defaultDescription}</p>

        {/* 操作按鈕 */}
        <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
          {showRetry && (
            <button
              onClick={handleRetry}
              className="flex items-center justify-center gap-2 rounded-lg bg-blue-600 px-6 py-3 text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <RefreshCw className="h-4 w-4" />
              重試
            </button>
          )}

          {showHome && (
            <button
              onClick={handleGoHome}
              className="flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-6 py-3 text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              <Home className="h-4 w-4" />
              返回首頁
            </button>
          )}
        </div>

        {/* 自定義操作 */}
        {actions && <div className="mt-6">{actions}</div>}

        {/* 技術支援提示 */}
        <div className="mt-8 text-sm text-gray-500">
          <p>如果問題持續發生，請聯繫技術支援</p>
        </div>
      </div>
    </div>
  );
};

export default ErrorPage;
