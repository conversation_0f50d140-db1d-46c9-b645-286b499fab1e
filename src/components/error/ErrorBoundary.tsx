import { AlertTriangle } from 'lucide-react';
import { Component, ErrorInfo, ReactNode } from 'react';

import ErrorPage from './ErrorPage';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  /** 自定義錯誤頁面 */
  fallback?: ReactNode;
  /** 錯誤回調函數 */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  /** 是否在開發環境顯示詳細錯誤信息 */
  showErrorDetails?: boolean;
}

/**
 * 全局錯誤邊界組件
 * 捕獲 React 組件樹中的 JavaScript 錯誤
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 更新 state 以顯示錯誤 UI
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 記錄錯誤信息
    console.error('ErrorBoundary 捕獲到錯誤:', error, errorInfo);

    // 更新狀態以包含錯誤詳情
    this.setState({
      error,
      errorInfo,
    });

    // 調用自定義錯誤處理函數
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 在生產環境中，可以將錯誤發送到錯誤報告服務
    if (import.meta.env.PROD) {
      // TODO: 發送錯誤到監控服務 (如 Sentry)
      // reportErrorToService(error, errorInfo);
    }
  }

  handleRetry = () => {
    // 重置錯誤狀態
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果有自定義錯誤頁面，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 檢查是否為代碼分割載入錯誤
      const isChunkLoadError =
        this.state.error?.name === 'ChunkLoadError' ||
        this.state.error?.message?.includes('Loading chunk') ||
        this.state.error?.message?.includes('Loading CSS chunk');

      if (isChunkLoadError) {
        return (
          <ErrorPage
            type="generic"
            title="應用程式更新"
            description="應用程式已更新，請重新載入頁面"
            icon={<AlertTriangle className="h-16 w-16 text-blue-500" />}
            showRetry={true}
            showHome={false}
            onRetry={() => window.location.reload()}
            actions={
              <button
                onClick={() => window.location.reload()}
                className="mt-4 rounded-lg bg-blue-600 px-6 py-2 text-white hover:bg-blue-700"
              >
                重新載入頁面
              </button>
            }
          />
        );
      }

      // 預設錯誤頁面
      return (
        <ErrorPage
          type="generic"
          title="應用程式發生錯誤"
          description="很抱歉，應用程式遇到了意外錯誤"
          icon={<AlertTriangle className="h-16 w-16 text-red-500" />}
          showRetry={true}
          showHome={true}
          onRetry={this.handleRetry}
          actions={
            this.props.showErrorDetails && import.meta.env.DEV ? (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                  顯示錯誤詳情 (開發模式)
                </summary>
                <div className="mt-4 rounded-lg bg-gray-100 p-4 text-xs">
                  <div className="mb-2">
                    <strong>錯誤:</strong> {this.state.error?.toString()}
                  </div>
                  <div className="mb-2">
                    <strong>堆疊追蹤:</strong>
                    <pre className="mt-1 whitespace-pre-wrap text-xs">
                      {this.state.error?.stack}
                    </pre>
                  </div>
                  {this.state.errorInfo && (
                    <div>
                      <strong>組件堆疊:</strong>
                      <pre className="mt-1 whitespace-pre-wrap text-xs">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            ) : null
          }
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
