import dayjs from 'dayjs';
import React from 'react';

import { ViewModeEnum } from '@/lib/jotai/shiftScheduleApproval';
import { cn } from '@/utils/style';

export enum ShiftTypeEnum {
  WORK = 'work',
  LEAVE = 'leave',
  OVERTIME = 'overtime',
}

interface ShiftBadgeProps {
  shiftType?: ShiftTypeEnum;
  viewMode?: ViewModeEnum;
  shiftId?: string;
  title: string;
  startTime?: string;
  endTime?: string;
  colorCode?: string;
  className?: string;
  onHover?: () => void;
  onClick?: () => void;
}

// 工具函數：顏色工具函式
export function getColorWithOpacity(colorCode: string, opacity: number = 0.3): string {
  if (colorCode.startsWith('#')) {
    const hex = colorCode.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  return colorCode;
}

const ShiftBadge: React.FC<ShiftBadgeProps> = ({
  title,
  startTime,
  endTime,
  colorCode = '#99999B',
  className,
}) => {
  const baseClass = 'w-[108px] h-[42px] rounded p-1 text-[12px] flex justify-start gap-1';
  const backgroundColor = getColorWithOpacity(colorCode, 0.2);

  return (
    <React.Fragment>
      <div
        className={cn(baseClass, className)}
        style={{
          backgroundColor,
        }}
      >
        <div className="h-full w-1 rounded-full" style={{ backgroundColor: colorCode }} />
        <div className="flex flex-col text-[12px]">
          <div className="line-clamp-1 font-medium">{title}</div>
          <div>
            {startTime && endTime && (
              <div className="text-xs">
                {dayjs(startTime).format('HH:mm')} - {dayjs(endTime).format('HH:mm')}
              </div>
            )}
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default ShiftBadge;
