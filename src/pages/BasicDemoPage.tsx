import { Button } from '@mayo/mayo-ui-beta/v2';
import { useAtom } from 'jotai';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLoaderData } from 'react-router-dom';

import { useGetUserInfo } from '@/hooks/api/user/useGetUserInfo';
import { supportedLngs } from '@/i18n';
import { countAtom } from '@/lib/jotai/demo';
import { LanguageMenuNamesEnum } from '@/types/enums/common';

export function BasicDemoLoader() {
  // 這裡可以 fetch API 或 return Promise
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: 'Hello',
      });
    }, 1000);
  });
}

export async function BasicDemoAction({ request }: { request: Request }) {
  const formData = await request.formData();
  const data = Object.fromEntries(formData);
  console.log('🚀 ~ data:', data);
  return {
    data,
  };
}
const BasicDemoPage: React.FC = () => {
  const { data } = useLoaderData() as { data: string }; // 這裡可以拿到 loader 回傳的資料
  console.log('🚀 ~ loader data:', data);

  const { t, i18n } = useTranslation();
  const [jotaiCount, setJotaiCount] = useAtom(countAtom);

  const { data: userInfo, isLoading: userInfoLoading } = useGetUserInfo();
  console.log('🚀 ~ userInfo:', userInfo);

  return (
    <div>
      <h1 className="">Demo Page</h1>
      <p className="mt-2 text-gray-600">
        This is a demonstration of various features and components
      </p>
      <main className="space-y-8">
        {/* i18n demo */}
        <section className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-h3">i18n Demo</h3>
          <p>
            目前語系：<span className="font-semibold">{i18n.language}</span>
          </p>
          <p>
            支援語系：
            {supportedLngs.map(lng => LanguageMenuNamesEnum[lng]).join(', ')}
          </p>
          <div className="my-2">
            <p className="text-lg">{t('hello')}</p>
            <p className="text-lg">{t('welcome')}</p>
            <p className="text-lg">{t('goodbye')}</p>
          </div>
        </section>

        {/* Jotai demo */}
        <section className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-h3">Jotai Demo</h3>
          <p>Count: {jotaiCount}</p>
          <div className="flex space-x-2">
            <Button onClick={() => setJotaiCount(jotaiCount + 1)}>Increment</Button>
            <Button onClick={() => setJotaiCount(jotaiCount - 1)}>Decrement</Button>
          </div>
        </section>

        {/* Get User Info Mock API Demo */}
        <section className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-h3">User Info Mock API Demo</h3>
          <pre className="text-sm">
            {userInfoLoading ? 'Loading...' : JSON.stringify(userInfo, null, 2)}
          </pre>
        </section>
      </main>
    </div>
  );
};

export default BasicDemoPage;
