import React from 'react';

import { useDashboardStats } from '../hooks/api/useDashboard';
import { useCreateEmployee, useEmployees } from '../hooks/api/useEmployees';
import { useShifts } from '../hooks/api/useShifts';
import { useStores } from '../hooks/api/useStores';

const MSWDemoPage: React.FC = () => {
  // 使用 React Query hooks 替代原本的 useState 和 useEffect
  const {
    data: employeesData,
    isLoading: employeesLoading,
    error: employeesError,
    refetch: refetchEmployees,
  } = useEmployees({ limit: 5 });

  const {
    data: storesData,
    isLoading: storesLoading,
    error: storesError,
  } = useStores({ limit: 5 });

  const {
    data: shiftsData,
    isLoading: shiftsLoading,
    error: shiftsError,
  } = useShifts({ limit: 10 });

  const { data: statsData, isLoading: statsLoading, error: statsError } = useDashboardStats();

  // 創建員工的 mutation
  const createEmployeeMutation = useCreateEmployee();

  // 計算整體載入狀態
  const isLoading = employeesLoading || storesLoading || shiftsLoading || statsLoading;

  // 計算整體錯誤狀態
  const error = employeesError || storesError || shiftsError || statsError;

  // 手動重新載入所有資料
  const loadData = () => {
    refetchEmployees();
    // React Query 會自動處理其他資料的重新獲取
  };

  // 創建新員工 - 現在使用 mutation
  const createEmployee = async () => {
    try {
      await createEmployeeMutation.mutateAsync({
        name: `測試員工 ${Date.now()}`,
        department: '測試部門',
        position: '測試職位',
        color: '#FF6B6B',
      });
      // React Query 會自動更新快取，不需要手動重新載入
    } catch (err) {
      console.error('創建員工失敗:', err);
    }
  };

  // 載入狀態
  if (isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  // 錯誤狀態
  if (error) {
    return (
      <div className="p-6">
        <div className="rounded-md border border-red-200 bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">錯誤</h3>
              <div className="mt-2 text-sm text-red-700">{error.message}</div>
              <button
                onClick={loadData}
                className="mt-4 rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
              >
                重試
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 解構資料
  const employees = employeesData?.data || [];
  const stores = storesData?.data || [];
  const shifts = shiftsData?.data || [];
  const stats = statsData?.data;

  return (
    <div className="space-y-8 p-6">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-gray-900">MSW Demo 頁面</h1>

        {/* 控制按鈕 */}
        <div className="mb-8 rounded-lg bg-white p-6 shadow">
          <h2 className="mb-4 text-xl font-semibold">控制面板</h2>
          <div className="flex gap-4">
            <button
              onClick={loadData}
              className="rounded-md bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
            >
              重新載入資料
            </button>
            <button
              onClick={createEmployee}
              disabled={createEmployeeMutation.isPending}
              className="rounded-md bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700 disabled:opacity-50"
            >
              {createEmployeeMutation.isPending ? '創建中...' : '創建測試員工'}
            </button>
          </div>

          {/* 顯示 mutation 狀態 */}
          {createEmployeeMutation.isError && (
            <div className="mt-4 text-sm text-red-600">
              創建失敗: {createEmployeeMutation.error?.message}
            </div>
          )}
          {createEmployeeMutation.isSuccess && (
            <div className="mt-4 text-sm text-green-600">員工創建成功！</div>
          )}
        </div>

        {/* 統計資訊 */}
        {stats && (
          <div className="mb-8 rounded-lg bg-white p-6 shadow">
            <h2 className="mb-4 text-xl font-semibold">統計資訊</h2>
            <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
              <div className="rounded-lg bg-blue-50 p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.totalEmployees}</div>
                <div className="text-sm text-gray-600">總員工數</div>
              </div>
              <div className="rounded-lg bg-green-50 p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.totalStores}</div>
                <div className="text-sm text-gray-600">總門店數</div>
              </div>
              <div className="rounded-lg bg-purple-50 p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.totalShifts}</div>
                <div className="text-sm text-gray-600">總排班數</div>
              </div>
              <div className="rounded-lg bg-orange-50 p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.todayShifts}</div>
                <div className="text-sm text-gray-600">今日排班</div>
              </div>
            </div>
          </div>
        )}

        {/* 員工列表 */}
        <div className="mb-8 rounded-lg bg-white p-6 shadow">
          <h2 className="mb-4 text-xl font-semibold">員工列表 (前 5 名)</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    姓名
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    部門
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    職位
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    狀態
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {employees.map(employee => (
                  <tr key={employee.id}>
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="flex items-center">
                        <div
                          className="mr-3 h-4 w-4 rounded-full"
                          style={{ backgroundColor: employee.color }}
                        ></div>
                        <div className="text-sm font-medium text-gray-900">{employee.name}</div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                      {employee.department}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                      {employee.position}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <span
                        className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                          employee.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {employee.isActive ? '啟用' : '停用'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* 門店列表 */}
        <div className="mb-8 rounded-lg bg-white p-6 shadow">
          <h2 className="mb-4 text-xl font-semibold">門店列表 (前 5 名)</h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {stores.map(store => (
              <div key={store.id} className="rounded-lg border border-gray-200 p-4">
                <h3 className="font-semibold text-gray-900">{store.name}</h3>
                <p className="mt-1 text-sm text-gray-600">{store.location}</p>
                <div className="mt-2">
                  <span
                    className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                      store.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {store.isActive ? '營業中' : '停業'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 排班列表 */}
        <div className="rounded-lg bg-white p-6 shadow">
          <h2 className="mb-4 text-xl font-semibold">近期排班 (前 10 筆)</h2>
          <div className="space-y-3">
            {shifts.map(shift => (
              <div key={shift.id} className="rounded-lg border border-gray-200 p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="font-semibold text-gray-900">員工 ID: {shift.employeeId}</p>
                    <p className="text-sm text-gray-600">門店: {shift.storeId}</p>
                    <p className="text-sm text-gray-600">日期: {shift.date}</p>
                    {shift.notes && <p className="mt-1 text-sm text-gray-500">{shift.notes}</p>}
                  </div>
                  <div className="text-right">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                        shift.status === 'scheduled'
                          ? 'bg-yellow-100 text-yellow-800'
                          : shift.status === 'confirmed'
                            ? 'bg-blue-100 text-blue-800'
                            : shift.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {shift.status === 'scheduled'
                        ? '已排班'
                        : shift.status === 'confirmed'
                          ? '已確認'
                          : shift.status === 'completed'
                            ? '已完成'
                            : '已取消'}
                    </span>
                    <p className="mt-1 text-xs text-gray-500">
                      {new Date(shift.startTime).toLocaleTimeString()} -
                      {new Date(shift.endTime).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MSWDemoPage;
