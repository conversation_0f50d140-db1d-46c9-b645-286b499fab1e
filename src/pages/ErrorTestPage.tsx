import { FC } from 'react';

import { useErrorHandler } from '@/hooks/useErrorHandler';

/**
 * 錯誤測試頁面
 * 用於測試各種錯誤處理情境
 */
const ErrorTestPage: FC = () => {
  const {
    handleApiError,
    handleNetworkError,
    handleRuntimeError,
    handleChunkLoadError,
    showSuccessNotification,
    showWarningNotification,
    showErrorNotification,
  } = useErrorHandler();

  const testApiError = () => {
    const mockApiError = new Error('API 請求失敗') as Error & {
      apiError: {
        code: 'INTERNAL_SERVER_ERROR';
        message: '服務器內部錯誤，請稍後再試';
        status: 500;
        isRetryable: true;
        isNetworkError: false;
      };
    };
    mockApiError.apiError = {
      code: 'INTERNAL_SERVER_ERROR',
      message: '服務器內部錯誤，請稍後再試',
      status: 500,
      isRetryable: true,
      isNetworkError: false,
    };

    handleApiError(mockApiError, async () => {
      console.log('重試 API 請求...');
      // 模擬重試邏輯
      await new Promise(resolve => setTimeout(resolve, 1000));
    });
  };

  const testNetworkError = () => {
    handleNetworkError('無法連接到服務器，請檢查您的網路連接');
  };

  const testRuntimeError = () => {
    const error = new Error('模擬 JavaScript 運行時錯誤');
    error.stack =
      'Error: 模擬 JavaScript 運行時錯誤\n    at testRuntimeError (ErrorTestPage.tsx:45:19)';
    handleRuntimeError(error);
  };

  const testChunkLoadError = () => {
    handleChunkLoadError();
  };

  const testJavaScriptError = () => {
    // 這會觸發錯誤邊界
    throw new Error('測試 JavaScript 錯誤邊界');
  };

  const testNotifications = () => {
    showSuccessNotification('成功', '操作已成功完成');

    setTimeout(() => {
      showWarningNotification('警告', '這是一個警告訊息');
    }, 1000);

    setTimeout(() => {
      showErrorNotification('錯誤', '這是一個錯誤訊息', [
        {
          label: '重試',
          action: () => console.log('重試操作'),
          variant: 'primary',
        },
        {
          label: '取消',
          action: () => console.log('取消操作'),
          variant: 'secondary',
        },
      ]);
    }, 2000);
  };

  const test404Error = () => {
    // 導航到不存在的路由
    window.location.href = '/non-existent-route';
  };

  const testApiRequest = async () => {
    try {
      // 模擬 API 請求失敗
      const response = await fetch('/api/test-error');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('API 請求失敗:', error);
      if (error instanceof Error) {
        handleApiError(error as Error & { apiError?: any });
      }
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="mb-8 text-3xl font-bold text-gray-900">錯誤處理系統測試</h1>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* API 錯誤測試 */}
        <div className="rounded-lg border border-gray-200 p-6">
          <h2 className="mb-4 text-xl font-semibold text-gray-800">API 錯誤</h2>
          <div className="space-y-3">
            <button
              onClick={testApiError}
              className="w-full rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              測試 500 錯誤
            </button>
            <button
              onClick={testApiRequest}
              className="w-full rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              測試 API 請求失敗
            </button>
          </div>
        </div>

        {/* 網路錯誤測試 */}
        <div className="rounded-lg border border-gray-200 p-6">
          <h2 className="mb-4 text-xl font-semibold text-gray-800">網路錯誤</h2>
          <button
            onClick={testNetworkError}
            className="w-full rounded bg-orange-600 px-4 py-2 text-white hover:bg-orange-700"
          >
            測試網路連接失敗
          </button>
        </div>

        {/* 運行時錯誤測試 */}
        <div className="rounded-lg border border-gray-200 p-6">
          <h2 className="mb-4 text-xl font-semibold text-gray-800">運行時錯誤</h2>
          <div className="space-y-3">
            <button
              onClick={testRuntimeError}
              className="w-full rounded bg-purple-600 px-4 py-2 text-white hover:bg-purple-700"
            >
              測試運行時錯誤
            </button>
            <button
              onClick={testJavaScriptError}
              className="w-full rounded bg-purple-600 px-4 py-2 text-white hover:bg-purple-700"
            >
              測試錯誤邊界
            </button>
          </div>
        </div>

        {/* 代碼分割錯誤測試 */}
        <div className="rounded-lg border border-gray-200 p-6">
          <h2 className="mb-4 text-xl font-semibold text-gray-800">代碼載入錯誤</h2>
          <button
            onClick={testChunkLoadError}
            className="w-full rounded bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
          >
            測試代碼分割錯誤
          </button>
        </div>

        {/* 路由錯誤測試 */}
        <div className="rounded-lg border border-gray-200 p-6">
          <h2 className="mb-4 text-xl font-semibold text-gray-800">路由錯誤</h2>
          <button
            onClick={test404Error}
            className="w-full rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            測試 404 錯誤
          </button>
        </div>

        {/* 通知測試 */}
        <div className="rounded-lg border border-gray-200 p-6">
          <h2 className="mb-4 text-xl font-semibold text-gray-800">通知系統</h2>
          <button
            onClick={testNotifications}
            className="w-full rounded bg-green-600 px-4 py-2 text-white hover:bg-green-700"
          >
            測試通知系統
          </button>
        </div>
      </div>

      {/* 說明文字 */}
      <div className="mt-8 rounded-lg bg-gray-50 p-6">
        <h3 className="mb-4 text-lg font-semibold text-gray-800">測試說明</h3>
        <ul className="space-y-2 text-sm text-gray-600">
          <li>
            • <strong>API 錯誤</strong>: 測試 HTTP 狀態碼錯誤和重試機制
          </li>
          <li>
            • <strong>網路錯誤</strong>: 測試網路連接失敗的處理
          </li>
          <li>
            • <strong>運行時錯誤</strong>: 測試 JavaScript 錯誤和錯誤邊界
          </li>
          <li>
            • <strong>代碼載入錯誤</strong>: 測試代碼分割載入失敗
          </li>
          <li>
            • <strong>路由錯誤</strong>: 測試 404 頁面未找到
          </li>
          <li>
            • <strong>通知系統</strong>: 測試各種類型的通知顯示
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ErrorTestPage;
