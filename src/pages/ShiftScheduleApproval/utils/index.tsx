import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

import { WorkHoursTypeEnum } from '@/types/enums/shiftScheduleApproval';
import { SelectOption } from '@/types/interface/select';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

/**
 * 判斷該工時制是否需要 baseDate
 */
export const isBaseDateRequired = (workHoursType?: WorkHoursTypeEnum | string): boolean => {
  return [
    WorkHoursTypeEnum.TWO_WEEKS,
    WorkHoursTypeEnum.FOUR_WEEKS,
    WorkHoursTypeEnum.EIGHT_WEEKS,
  ].includes(workHoursType as WorkHoursTypeEnum);
};

/**
 * 根據一組週期起算日選項，取得預設的 baseDate。
 * !如果出勤設定找不到週期起算日，預設2018-01-01(後端會預設)
 * !預設為現在之前的最近日期，如果找不到，預設為第一筆
 * @param baseDateOptions SelectOption[]，value 格式 'YYYY-MM-DD'
 * @returns 預設 baseDate（SelectOption<string>），為今天之前最近的日期，找不到則回傳第一筆
 */
export function getDefaultBaseDate(baseDateOptions: SelectOption<string>[]): SelectOption<string> {
  let defaultBaseDate = baseDateOptions[0];
  const today = dayjs();
  const beforeOrToday = baseDateOptions.filter(opt =>
    dayjs(opt.value).isSameOrBefore(today, 'day'),
  );
  if (beforeOrToday.length > 0) {
    defaultBaseDate = beforeOrToday[beforeOrToday.length - 1];
  }
  return defaultBaseDate;
}

/**
 * 依據週期起算日與工時制，產生所有週期區間選項。
 * 每個選項為 [起日, 迄日]，label 為 'YYYY/MM/DD - YYYY/MM/DD'。
 * @param baseDate 週期起算日（字串，格式 'YYYY-MM-DD'）
 * @param workHoursType 工時制（2/4/8 週等）
 * @param endDate 結束日期（可選，預設為未來兩年）
 * @returns 週期區間選項（SelectOption<[string, string]>[]）
 */

const weeksByWorkHoursType: Record<WorkHoursTypeEnum, number> = {
  [WorkHoursTypeEnum.STANDARD]: 1,
  [WorkHoursTypeEnum.TWO_WEEKS]: 2,
  [WorkHoursTypeEnum.FOUR_WEEKS]: 4,
  [WorkHoursTypeEnum.EIGHT_WEEKS]: 8,
};

export const generatePeriodOptions = (
  baseDate: string,
  workHoursType: WorkHoursTypeEnum,
  endDate?: dayjs.Dayjs, // 可選，預設未來兩年
): SelectOption<[string, string]>[] => {
  const options: SelectOption<[string, string]>[] = [];
  const base = dayjs(baseDate);
  const weeks = weeksByWorkHoursType[workHoursType];

  if (!weeks) return [];

  const now = dayjs();
  let _endDate = endDate ?? dayjs().startOf('year').add(2, 'year').subtract(1, 'day');
  if (now.isBefore(base)) {
    _endDate = base.add(1, 'year');
  }
  let current = base.clone();
  while (current.isBefore(_endDate)) {
    const periodEnd = current.add(weeks * 7 - 1, 'day');
    options.push({
      label: `${current.format('YYYY/MM/DD')} - ${periodEnd.format('YYYY/MM/DD')}`,
      value: [current.format('YYYY-MM-DD'), periodEnd.format('YYYY-MM-DD')],
    });
    current = current.add(weeks, 'week');
  }

  return options;
};

/**
 * 從週期區間選項中，找到今天對應的那一個週期選項
 * @param options  週期區間選項（SelectOption<[string, string]>[]）
 * @returns 包含今天的那個週期選項，找不到則回傳 undefined
 */
export const findTodayPeriodOption = (
  options: SelectOption<[string, string]>[],
): SelectOption<[string, string]> | undefined => {
  const today = dayjs();
  return options.find(opt => {
    const [start, end] = opt.value;
    return today.isSameOrAfter(dayjs(start), 'day') && today.isSameOrBefore(dayjs(end), 'day');
  });
};

/**
 * 生成指定日期區間的所有日期
 * @param startDate 開始日期
 * @param endDate 結束日期
 * @returns 日期陣列
 */
export const getDaysInRange = (startDate: string, endDate: string) => {
  const days = [];
  let currentDate = dayjs(startDate);

  while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
    days.push(currentDate.format('YYYY-MM-DD'));
    currentDate = currentDate.add(1, 'day');
  }

  return days;
};

/**
 * 取得星期幾的對應
 * @param t 翻譯函數
 * @returns 星期幾的對應
 */
// TODO 待確認 dayjs localize 功能是否能直接轉換
export const getWeekdayMap = (t: (key: string) => string) => ({
  // 星期對應表
  0: t('日'),
  1: t('一'),
  2: t('二'),
  3: t('三'),
  4: t('四'),
  5: t('五'),
  6: t('六'),
});
