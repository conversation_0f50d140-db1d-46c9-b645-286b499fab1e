import dayjs from 'dayjs';

import { Calendar, Employee, ShiftScheduleDetail } from '@/hooks/api/useGetShiftSchedule';
import { CalendarDayTypeEnum, CycleTypeEnum } from '@/types/enums/shiftScheduleApproval';

import { SHIFT_CONSTANTS } from '../constants';

/**
 * 班次業務邏輯模組
 *
 * 此模組負責處理班次排程的核心業務邏輯，包含：
 * 1. 班次狀態判斷
 * 2. 班次分類計算
 * 3. 渲染條件判斷
 * 4. 統計數據計算
 *
 * 所有函數都設計為純函數，便於測試和維護
 *
 * 採用 Factory Pattern 處理班次狀態物件產生：
 * - 所有 ShiftStatusResult 物件皆由 createShiftStatus(type, options) 統一產生
 * - 這樣可避免重複物件建構，方便日後擴充與維護
 * - 若需新增狀態，只需擴充 ShiftStatusType 與 createShiftStatus
 */

/**
 * 班次狀態判斷結果介面
 *
 * 用於表示某一天的班次安排狀態
 */
export interface ShiftStatusResult {
  isHaveSchedule: boolean; // 是否有正常排班
  isDayOff: boolean; // 是否一班休假
  isMonthLeave: boolean; // 是否月休
  isRestDay: boolean; // 是否休息日
  isUsualHoliday: boolean; // 是否例假日
  isSupportSchedule: boolean; // 是否為支援排班
  isNoSchedule: boolean; // 是否無排班
  isHoliday: boolean; // 是否為假期
}

/**
 * 基本班次信息提取
 *
 * 從行事曆資料中提取基本的班次信息，用於後續的業務邏輯判斷
 */
interface BasicShiftInfo {
  hasShift: boolean;
  isSupport: boolean;
  eventStatus: number | null;
  cycleStatus: number | undefined;
  shiftId: string | undefined;
  itemOptionId: string | null | undefined;
  eventItemOptionId: string | null | undefined;
}

function getBasicShiftInfo(dayCalendar: Calendar): BasicShiftInfo {
  const event = dayCalendar.calendarEvent;
  return {
    hasShift: Boolean(dayCalendar.shiftSchedule),
    isSupport: Boolean(dayCalendar.supportDeptId),
    eventStatus: event?.eventStatus ?? null,
    cycleStatus: dayCalendar.shiftSchedule?.cycleStatus,
    shiftId: dayCalendar.shiftSchedule?.shiftScheduleId,
    itemOptionId: dayCalendar.itemOptionId,
    eventItemOptionId: event?.itemOptionId,
  };
}

/**
 * 統一的班次狀態工廠
 *
 * 創建 ShiftStatusResult 對象，避免重複的對象構建代碼
 */
type ShiftStatusType =
  | 'noSchedule'
  | 'work'
  | 'support'
  | 'holiday'
  | 'restDay'
  | 'monthLeave'
  | 'officialHoliday'
  | 'dayOff';

function createShiftStatus(
  type: ShiftStatusType,
  options: Partial<ShiftStatusResult> = {},
): ShiftStatusResult {
  const baseStatus: ShiftStatusResult = {
    isHaveSchedule: false,
    isSupportSchedule: false,
    isNoSchedule: false,
    isHoliday: false,
    isDayOff: false,
    isMonthLeave: false,
    isRestDay: false,
    isUsualHoliday: false,
  };

  switch (type) {
    case 'noSchedule':
      return { ...baseStatus, isNoSchedule: true, ...options };
    case 'work':
      return { ...baseStatus, isHaveSchedule: true, ...options };
    case 'support':
      return { ...baseStatus, isSupportSchedule: true, ...options };
    case 'holiday':
      return { ...baseStatus, isHoliday: true, ...options };
    case 'restDay':
      return { ...baseStatus, isRestDay: true, ...options };
    case 'monthLeave':
      return { ...baseStatus, isMonthLeave: true, ...options };
    case 'officialHoliday':
      return { ...baseStatus, isUsualHoliday: true, ...options };
    case 'dayOff':
      return { ...baseStatus, isDayOff: true, ...options };
    default:
      return { ...baseStatus, ...options };
  }
}

/**
 * 可複用的業務邏輯檢查函數
 */

// 休息日類型判斷
function isRestDayType(dayCalendar: Calendar): boolean {
  const info = getBasicShiftInfo(dayCalendar);
  return (
    info.itemOptionId === CycleTypeEnum.REST ||
    info.eventItemOptionId === CalendarDayTypeEnum.REST_DAY ||
    (info.cycleStatus === SHIFT_CONSTANTS.CYCLE_STATUS.HOLIDAY &&
      info.shiftId === SHIFT_CONSTANTS.REST_DAY_ID)
  );
}

// 一般假期類型判斷
function isOfficialHolidayType(dayCalendar: Calendar): boolean {
  const info = getBasicShiftInfo(dayCalendar);
  return (
    info.itemOptionId === CycleTypeEnum.OFFICIAL ||
    info.eventItemOptionId === CalendarDayTypeEnum.OFFICIAL_HOLIDAY ||
    (info.cycleStatus === SHIFT_CONSTANTS.CYCLE_STATUS.HOLIDAY &&
      info.shiftId === SHIFT_CONSTANTS.USUAL_HOLIDAY_ID)
  );
}

// 假日出勤衝突檢查
function hasHolidayScheduleConflict(dayCalendar: Calendar): boolean {
  const info = getBasicShiftInfo(dayCalendar);
  return Boolean(
    dayCalendar.calendarEvent &&
      (info.eventStatus === SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_1 ||
        info.eventStatus === SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_2) &&
      dayCalendar.shiftSchedule &&
      info.cycleStatus === SHIFT_CONSTANTS.CYCLE_STATUS.HOLIDAY,
  );
}

// 一般休假判斷（複雜條件）
function isGeneralLeaveType(dayCalendar: Calendar): boolean {
  const info = getBasicShiftInfo(dayCalendar);
  return (
    info.cycleStatus === SHIFT_CONSTANTS.CYCLE_STATUS.HOLIDAY &&
    info.itemOptionId !== CycleTypeEnum.REST &&
    info.itemOptionId !== CycleTypeEnum.OFFICIAL &&
    !(
      dayCalendar.calendarEvent &&
      dayCalendar.calendarEvent.itemOptionId === CalendarDayTypeEnum.REST_DAY
    ) &&
    !(
      dayCalendar.calendarEvent &&
      dayCalendar.calendarEvent.itemOptionId === CalendarDayTypeEnum.OFFICIAL_HOLIDAY
    )
  );
}

/**
 * 決策鏈檢查函數
 *
 * 每個函數負責特定的業務邏輯判斷，返回 ShiftStatusResult 或 null
 */

// 檢查特殊假日
function checkSpecialHoliday(dayCalendar: Calendar): ShiftStatusResult | null {
  const info = getBasicShiftInfo(dayCalendar);
  if (info.shiftId === SHIFT_CONSTANTS.HOLIDAY_ID) {
    return createShiftStatus('noSchedule', { isHoliday: true });
  }
  return null;
}

// 檢查假日出勤衝突
function checkHolidayScheduleConflict(dayCalendar: Calendar): ShiftStatusResult | null {
  if (hasHolidayScheduleConflict(dayCalendar)) {
    return createShiftStatus('noSchedule');
  }
  return null;
}

// 檢查工作日
function checkWorkDay(dayCalendar: Calendar): ShiftStatusResult | null {
  const info = getBasicShiftInfo(dayCalendar);
  if (info.cycleStatus === SHIFT_CONSTANTS.CYCLE_STATUS.WORK) {
    return createShiftStatus('work');
  }
  return null;
}

// 檢查一般休假
function checkGeneralLeave(dayCalendar: Calendar): ShiftStatusResult | null {
  if (isGeneralLeaveType(dayCalendar)) {
    return createShiftStatus('work');
  }
  return null;
}

// 檢查月休
function checkMonthLeave(dayCalendar: Calendar): ShiftStatusResult | null {
  const info = getBasicShiftInfo(dayCalendar);
  if (
    info.cycleStatus === SHIFT_CONSTANTS.CYCLE_STATUS.HOLIDAY &&
    info.shiftId === SHIFT_CONSTANTS.DEFAULT_MONTH_LEAVE_ID
  ) {
    return createShiftStatus('monthLeave');
  }
  return null;
}

// 檢查休息日
function checkRestDay(dayCalendar: Calendar): ShiftStatusResult | null {
  if (isRestDayType(dayCalendar)) {
    return createShiftStatus('restDay');
  }
  return null;
}

// 檢查一般假期
function checkOfficialHoliday(dayCalendar: Calendar): ShiftStatusResult | null {
  if (isOfficialHolidayType(dayCalendar)) {
    return createShiftStatus('officialHoliday');
  }
  return null;
}

// 檢查支援排班
function checkSupportSchedule(dayCalendar: Calendar): ShiftStatusResult | null {
  const info = getBasicShiftInfo(dayCalendar);
  if (info.hasShift && info.isSupport) {
    return createShiftStatus('support', {
      isHaveSchedule: false,
      isNoSchedule:
        !info.hasShift &&
        (!dayCalendar.calendarEvent ||
          [
            SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_1,
            SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_2,
          ].includes(
            (info.eventStatus === 3 || info.eventStatus === 4 ? info.eventStatus : undefined) as
              | 3
              | 4,
          )),
      isHoliday: info.eventStatus === SHIFT_CONSTANTS.EVENT_STATUS.HOLIDAY && !info.isSupport,
    });
  }
  return null;
}

// 檢查無排班
function checkNoSchedule(dayCalendar: Calendar): ShiftStatusResult | null {
  const info = getBasicShiftInfo(dayCalendar);
  const isNoSchedule =
    !info.hasShift &&
    (!dayCalendar.calendarEvent ||
      [
        SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_1,
        SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_2,
      ].includes(
        (info.eventStatus === 3 || info.eventStatus === 4 ? info.eventStatus : undefined) as 3 | 4,
      ));

  if (isNoSchedule) {
    return createShiftStatus('noSchedule', {
      isHaveSchedule: false,
      isSupportSchedule: false,
      isHoliday: info.eventStatus === SHIFT_CONSTANTS.EVENT_STATUS.HOLIDAY && !info.isSupport,
    });
  }
  return null;
}

// 檢查行事曆假期
function checkCalendarHoliday(dayCalendar: Calendar): ShiftStatusResult | null {
  const info = getBasicShiftInfo(dayCalendar);
  const isHoliday = info.eventStatus === SHIFT_CONSTANTS.EVENT_STATUS.HOLIDAY && !info.isSupport;

  if (isHoliday) {
    return createShiftStatus('holiday', {
      isHaveSchedule: false,
      isSupportSchedule: false,
      isNoSchedule:
        !info.hasShift &&
        (!dayCalendar.calendarEvent ||
          [
            SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_1,
            SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_2,
          ].includes(
            (info.eventStatus === 3 || info.eventStatus === 4 ? info.eventStatus : undefined) as
              | 3
              | 4,
          )),
    });
  }
  return null;
}

/**
 * 判斷指定日期的班次狀態
 *
 * 此函數是班次狀態判斷的核心邏輯，根據行事曆資料判斷該日的排班狀態
 *
 * @param dayCalendar - 當日的行事曆資料，包含班次、事件等信息
 * @returns ShiftStatusResult - 包含各種狀態的判斷結果
 *
 * 重構後的判斷邏輯：
 * 1. 使用決策鏈模式處理複雜的條件判斷
 * 2. 每個檢查函數負責特定的業務邏輯
 * 3. 提高代碼可讀性和可維護性
 *
 * 優先級：特殊假日 > 假日衝突 > 支援排班 > 工作日 > 月休 > 休息日 > 一般假期 > 一般休假 > 無排班 > 行事曆假期
 */
export function determineShiftStatus(dayCalendar: Calendar | undefined): ShiftStatusResult {
  // 如果沒有行事曆資料，返回無排班狀態
  if (!dayCalendar) {
    return createShiftStatus('noSchedule');
  }

  const info = getBasicShiftInfo(dayCalendar);

  // 首先檢查是否有排班
  if (
    info.hasShift &&
    !info.isSupport &&
    info.eventStatus !== SHIFT_CONSTANTS.EVENT_STATUS.HOLIDAY
  ) {
    // 使用決策鏈處理有排班的情況
    return (
      checkSpecialHoliday(dayCalendar) ||
      checkHolidayScheduleConflict(dayCalendar) ||
      checkWorkDay(dayCalendar) ||
      checkGeneralLeave(dayCalendar) ||
      checkMonthLeave(dayCalendar) ||
      checkRestDay(dayCalendar) ||
      checkOfficialHoliday(dayCalendar) ||
      createShiftStatus('work') // 預設為工作日
    );
  }

  // 處理其他情況
  return (
    checkSupportSchedule(dayCalendar) ||
    checkNoSchedule(dayCalendar) ||
    checkCalendarHoliday(dayCalendar) ||
    createShiftStatus('noSchedule', {
      isHaveSchedule: false,
      isSupportSchedule: false,
      isHoliday: false,
    })
  );
}

/**
 * 班次分類類型
 *
 * 用於統計計算的班次分類
 */
export type ShiftCategory = 'work' | 'leave' | 'overtime';

/**
 * 根據排班狀態獲取班次分類
 *
 * 此函數將複雜的排班狀態簡化為三種基本分類，用於統計計算
 *
 * @param calendar - 行事曆日期資料
 * @returns ShiftCategory - 班次分類（work/leave/overtime
 *
 * 分類邏輯：
 * - schedulingStatus = 3：例假日 → leave
 * - schedulingStatus = 0：無排班 → rest
 * - schedulingStatus = 1：有排班，需進一步判斷循環狀態
 *   - cycleStatus = 2（休假日循環）→ rest
 *   - 其他情況 → work
 */
export function getShiftCategory(calendar: Calendar): ShiftCategory {
  switch (calendar.schedulingStatus) {
    case 3: // 例假日
      return 'leave';
    case 0: // 無排班
      return 'overtime';
    case 1: // 有排班
      if (calendar.shiftSchedule) {
        // 檢查循環狀態，如果是休假日循環則歸類為休息
        if (calendar.shiftSchedule.cycleStatus === SHIFT_CONSTANTS.CYCLE_STATUS.HOLIDAY) {
          return 'overtime'; // 休假日
        }
        return 'work'; // 工作日
      }
      return 'overtime';
    default:
      return 'overtime';
  }
}

/**
 * 班次渲染條件介面
 *
 * 定義各種渲染條件的判斷結果，用於決定 UI 組件的顯示邏輯
 */
export interface ShiftRenderConditions {
  isHolidaySpecial: boolean; // 是否為特殊節假日
  isNoScheduleWithVacationCycle: boolean; // 是否為無排班且休假循環（循環狀態為2）
  shouldShowWorkDay: boolean; // 是否顯示工作日組件
  shouldShowDayOff: boolean; // 是否顯示補休標籤
  shouldShowMonthLeave: boolean; // 是否顯示全月請假標籤
  shouldShowRestDay: boolean; // 是否顯示休息日標籤
  shouldShowUsualHoliday: boolean; // 是否顯示一般假期標籤
  shouldShowAdjustmentMark: boolean; // 是否顯示調班標記
}

/**
 * 獲取班次渲染條件
 *
 * 此函數是渲染邏輯的核心，根據複雜的業務規則判斷各種 UI 組件的顯示條件
 *
 * @param calendar - 行事曆資料
 * @param shiftSchedule - 班次排程資料
 * @returns ShiftRenderConditions - 各種渲染條件的判斷結果
 *
 * 判斷邏輯說明：
 * 1. 特殊節假日：班次ID為節假日ID
 * 2. 無排班且休假循環：有事件、狀態為無排班（3或4）、且為休假循環
 * 3. 工作日顯示：循環狀態不為休假
 * 4. 補休顯示：循環狀態為休假且不是預定義的休息日或假期選項
 * 5. 全月請假：循環狀態為休假且班次ID為全月請假ID
 * 6. 休息日：符合休息日的多種條件之一
 * 7. 一般假期：符合一般假期的多種條件之一
 * 8. 調班標記：工作時間已變更
 */
export function getShiftRenderConditions(
  calendar: Calendar,
  shiftSchedule: ShiftScheduleDetail,
): ShiftRenderConditions {
  const { calendarEvent, itemOptionId } = calendar;
  const shiftId = shiftSchedule.shiftScheduleId;
  const cycleStatus = shiftSchedule.cycleStatus ?? 0;
  const isVacationCycle = cycleStatus === SHIFT_CONSTANTS.CYCLE_STATUS.HOLIDAY; // 是否為休假日循環
  const status = calendarEvent?.eventStatus;
  const evItemOption = calendarEvent?.itemOptionId;

  return {
    // 特殊節假日：班次ID為預定義的節假日ID
    isHolidaySpecial: shiftId === SHIFT_CONSTANTS.HOLIDAY_ID,

    // 無排班且休假循環：有事件、狀態為無排班（3或4）、且為休假循環
    isNoScheduleWithVacationCycle: Boolean(
      calendarEvent &&
        (status === SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_1 ||
          status === SHIFT_CONSTANTS.EVENT_STATUS.NO_SCHEDULE_2) &&
        isVacationCycle,
    ),

    // 工作日顯示：非休假循環
    shouldShowWorkDay: !isVacationCycle,

    // 補休顯示：休假循環且不是預定義的休息日或假期選項
    shouldShowDayOff:
      isVacationCycle &&
      ![SHIFT_CONSTANTS.ITEM_OPTIONS.REST_DAY, SHIFT_CONSTANTS.ITEM_OPTIONS.USUAL_HOLIDAY].includes(
        itemOptionId as
          | typeof SHIFT_CONSTANTS.ITEM_OPTIONS.REST_DAY
          | typeof SHIFT_CONSTANTS.ITEM_OPTIONS.USUAL_HOLIDAY,
      ) &&
      ![
        SHIFT_CONSTANTS.EVENT_OPTIONS.REST_DAY,
        SHIFT_CONSTANTS.EVENT_OPTIONS.USUAL_HOLIDAY,
      ].includes(
        evItemOption as
          | typeof SHIFT_CONSTANTS.EVENT_OPTIONS.REST_DAY
          | typeof SHIFT_CONSTANTS.EVENT_OPTIONS.USUAL_HOLIDAY,
      ),

    // 全月請假顯示：休假循環且班次ID為全月請假ID
    shouldShowMonthLeave: isVacationCycle && shiftId === SHIFT_CONSTANTS.DEFAULT_MONTH_LEAVE_ID,

    // 休息日顯示：符合以下任一條件
    // 1. 休假循環且班次ID為休息日ID
    // 2. 項目選項為休息日
    // 3. 事件項目選項為休息日
    shouldShowRestDay:
      (isVacationCycle && shiftId === SHIFT_CONSTANTS.REST_DAY_ID) ||
      itemOptionId === SHIFT_CONSTANTS.ITEM_OPTIONS.REST_DAY ||
      evItemOption === SHIFT_CONSTANTS.EVENT_OPTIONS.REST_DAY,

    // 一般假期顯示：符合以下任一條件
    // 1. 休假循環且班次ID為一般假期ID
    // 2. 項目選項為一般假期
    // 3. 事件項目選項為一般假期
    shouldShowUsualHoliday:
      (isVacationCycle && shiftId === SHIFT_CONSTANTS.USUAL_HOLIDAY_ID) ||
      itemOptionId === SHIFT_CONSTANTS.ITEM_OPTIONS.USUAL_HOLIDAY ||
      evItemOption === SHIFT_CONSTANTS.EVENT_OPTIONS.USUAL_HOLIDAY,

    // 調班標記：工作時間已變更
    shouldShowAdjustmentMark: shiftSchedule.isWorkTimeChanged,
  };
}

/**
 * 出勤統計介面
 *
 * 用於記錄各種出勤狀態的統計數據
 */
export interface AttendanceStats {
  totalWorkTime: number; // 上班天數
  totalLeaveTime: number; // 請假天數
  totalOverTime: number; // 加班天數
}

/**
 * 計算總體出勤統計
 *
 * 統計所有員工的出勤情況總和
 *
 * @param employees - 員工陣列，每個員工包含其行事曆資料
 * @returns AttendanceStats - 總體出勤統計結果
 *
 * 計算邏輯：
 * 1. 遍歷所有員工
 * 2. 對每個員工的每一天進行分類統計
 * 3. 累計所有員工的統計結果
 */
export function calculateTotalStats(employees: Employee[]): AttendanceStats {
  const totalStats: AttendanceStats = { totalWorkTime: 0, totalLeaveTime: 0, totalOverTime: 0 };

  employees.forEach(employee => {
    totalStats.totalWorkTime += employee.totalWorkTime;
    totalStats.totalLeaveTime += employee.totalLeaveTime;
    totalStats.totalOverTime += employee.totalOverTime;
  });

  return totalStats;
}

/**
 * 計算班次的工作時數
 *
 * 根據班次的上班時間和下班時間計算實際工作時數，扣除休息時間
 *
 * @param shiftSchedule - 班次排程資料
 * @returns number - 工作時數（小時）
 *
 * 計算邏輯：
 * 1. 如果沒有上班時間或下班時間，返回 0
 * 2. 計算總工作時間（下班時間 - 上班時間）
 * 3. 扣除休息時間（如果有的話）
 * 4. 返回以小時為單位的工作時數
 */
export function calculateShiftWorkHours(shiftSchedule: ShiftScheduleDetail | null): number {
  if (!shiftSchedule || !shiftSchedule.workOnTime || !shiftSchedule.workOffTime) {
    return 0;
  }

  try {
    const workOnTime = new Date(shiftSchedule.workOnTime);
    const workOffTime = new Date(shiftSchedule.workOffTime);

    // 計算總工作時間（毫秒）
    let totalWorkMilliseconds = workOffTime.getTime() - workOnTime.getTime();

    // 扣除休息時間
    if (shiftSchedule.restTimeStart1 && shiftSchedule.restTimeEnd1) {
      const restStart1 = new Date(shiftSchedule.restTimeStart1);
      const restEnd1 = new Date(shiftSchedule.restTimeEnd1);
      totalWorkMilliseconds -= restEnd1.getTime() - restStart1.getTime();
    }

    if (shiftSchedule.restTimeStart2 && shiftSchedule.restTimeEnd2) {
      const restStart2 = new Date(shiftSchedule.restTimeStart2);
      const restEnd2 = new Date(shiftSchedule.restTimeEnd2);
      totalWorkMilliseconds -= restEnd2.getTime() - restStart2.getTime();
    }

    if (shiftSchedule.restTimeStart3 && shiftSchedule.restTimeEnd3) {
      const restStart3 = new Date(shiftSchedule.restTimeStart3);
      const restEnd3 = new Date(shiftSchedule.restTimeEnd3);
      totalWorkMilliseconds -= restEnd3.getTime() - restStart3.getTime();
    }

    // 轉換為小時
    return Math.max(0, totalWorkMilliseconds / (1000 * 60 * 60));
  } catch (error) {
    console.error('計算工作時數時發生錯誤:', error);
    return 0;
  }
}

/**
 * 每日出勤時數統計介面
 *
 * 用於記錄每日各種出勤狀態的時數統計
 */
export interface DailyAttendanceHours {
  totalWorkHours: number; // 正常排班工作時數
  totalSupportHours: number; // 支援排班工作時數
  totalHours: number; // 總工作時數
}

/**
 * 計算指定日期的出勤時數
 *
 * 使用 determineShiftStatus 函式判斷班次狀態，統計該日期的總工作時數
 *
 * @param date - 指定日期（YYYY-MM-DD 格式）
 * @param employees - 員工陣列
 * @returns DailyAttendanceHours - 該日期的出勤時數統計
 *
 * 計算邏輯：
 * 1. 遍歷所有員工
 * 2. 找到每個員工在指定日期的行事曆記錄
 * 3. 使用 determineShiftStatus 判斷班次狀態
 * 4. 根據狀態計算對應的工作時數
 * 5. 累計所有員工的工作時數
 */
export function calculateDailyAttendanceHours(
  date: string,
  employees: Employee[],
): DailyAttendanceHours {
  let totalWorkHours = 0;
  let totalSupportHours = 0;

  employees.forEach(employee => {
    // 在員工的行事曆中找到指定日期的記錄
    const dayCalendar = employee.calendars.find((calendar: Calendar) => {
      // 使用 Day.js 的 isSame 方法進行日期比較，更加高效和準確
      const calendarDate = dayjs(calendar.date);
      const targetDate = dayjs(date);
      return calendarDate.isSame(targetDate, 'day');
    });

    // 如果找到該日期的記錄，使用 determineShiftStatus 判斷狀態
    if (dayCalendar) {
      const shiftStatus = determineShiftStatus(dayCalendar);

      // 根據班次狀態計算工作時數
      if (shiftStatus.isHaveSchedule) {
        // 正常排班：計算工作時數
        const workHours = calculateShiftWorkHours(dayCalendar.shiftSchedule);
        totalWorkHours += workHours;
      } else if (shiftStatus.isSupportSchedule) {
        // 支援排班：計算支援工作時數
        const supportHours = calculateShiftWorkHours(dayCalendar.shiftSchedule);
        totalSupportHours += supportHours;
      }
      // 無排班和假期不計算工作時數
    }
  });

  return {
    totalWorkHours,
    totalSupportHours,
    totalHours: totalWorkHours + totalSupportHours,
  };
}

/**
 * 計算指定日期的出勤人數
 *
 * 統計在指定日期有多少員工需要上班，包括正常排班和支援排班
 *
 * @param date - 指定日期（YYYY-MM-DD 格式）
 * @param employees - 員工陣列
 * @returns number - 該日期的出勤人數
 *
 * 計算邏輯：
 * 1. 遍歷所有員工
 * 2. 找到每個員工在指定日期的行事曆記錄
 * 3. 使用 determineShiftStatus 判斷班次狀態
 * 4. 累計正常排班和支援排班的員工數量
 * 5. 邏輯與 calculateDailyAttendanceHours 保持一致
 */
export function calculateDailyAttendanceCount(date: string, employees: Employee[]): number {
  let attendanceCount = 0;

  employees.forEach(employee => {
    // 在員工的行事曆中找到指定日期的記錄
    const dayCalendar = employee.calendars.find((calendar: Calendar) => {
      // 使用 Day.js 的 isSame 方法進行日期比較，更加高效和準確
      const calendarDate = dayjs(calendar.date);
      const targetDate = dayjs(date);
      return calendarDate.isSame(targetDate, 'day');
    });

    // 如果找到該日期的記錄，使用 determineShiftStatus 判斷狀態
    if (dayCalendar) {
      const shiftStatus = determineShiftStatus(dayCalendar);

      // 計算出勤人數：正常排班 + 支援排班
      if (shiftStatus.isHaveSchedule || shiftStatus.isSupportSchedule) {
        attendanceCount++;
      }
      // 無排班和假期不計入出勤人數
    }
  });

  return attendanceCount;
}
