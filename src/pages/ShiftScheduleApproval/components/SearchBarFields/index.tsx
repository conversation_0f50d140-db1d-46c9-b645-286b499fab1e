import { FormControl, FormField, Select, Separator } from '@mayo/mayo-ui-beta/v2';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { isEmpty } from 'lodash';
import { CalendarIcon } from 'lucide-react';
import { useCallback, useEffect, useRef } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useGetBaseDateOptions } from '@/hooks/api/useGetBaseDateOptions';
import { useGetScheduleDepartments } from '@/hooks/api/useGetScheduleDepartments';
import { SearchBarForm } from '@/hooks/useSearchBarForm';
import { WorkHoursTypeEnum } from '@/types/enums/shiftScheduleApproval';
import type { SelectOption } from '@/types/interface/select';
import { cn } from '@/utils/style';

import { getDefaultBaseDate, isBaseDateRequired } from '../../utils';

dayjs.extend(isSameOrBefore);

interface SearchBarFieldsProps {
  departmentOptions: SelectOption[];
  workHoursTypeOptions: SelectOption[];
}

const SELECT_WIDTH = 'min-w-[140px] max-w-[260px] flex-1';

const SearchBarFields: React.FC<SearchBarFieldsProps> = ({
  departmentOptions,
  workHoursTypeOptions,
}) => {
  const { t } = useTranslation();
  const { isLoading: isGetScheduleDepartmentsLoading } = useGetScheduleDepartments();
  const {
    control,
    trigger,
    setValue,
    formState: { isSubmitting, isLoading: isFormLoading },
  } = useFormContext<SearchBarForm>();

  const [department, workHoursType] = useWatch({
    control,
    name: ['department', 'workHoursType'],
  }) as [SelectOption<string>, SelectOption<string>];

  const { data: baseDateOptions, isLoading: isBaseDateOptionsLoading } = useGetBaseDateOptions({
    params: {
      departmentId: department?.value,
      workHoursType: workHoursType?.value,
    },
    enabled:
      !!department?.value &&
      !!workHoursType?.value &&
      workHoursType.value !== WorkHoursTypeEnum.STANDARD,
  });

  const isDisabled =
    isGetScheduleDepartmentsLoading || isFormLoading || isSubmitting || isBaseDateOptionsLoading;

  // 處理欄位變更的通用邏輯
  const handleFieldChange = useCallback(
    (fieldName: keyof SearchBarForm) => {
      trigger(fieldName);
    },
    [trigger],
  );

  // 處理下拉選單變更
  const handleSelectChange = (fieldName: keyof SearchBarForm) => (option: SelectOption) => {
    if (!option.value) return;
    setValue(fieldName, {
      label: option.label,
      value: option.value as string,
    });
    handleFieldChange(fieldName);
  };

  // 處理日期變更
  const handleDateChange = (option: SelectOption<string>) => {
    setValue('baseDate', option.value);
    handleFieldChange('baseDate');
  };

  // 取得週期起算日
  const prevDeps = useRef<{ department?: string; workHoursType?: string }>({});

  useEffect(() => {
    const dep = department?.value;
    const wht = workHoursType?.value;
    const isChanged = prevDeps.current.department !== dep || prevDeps.current.workHoursType !== wht;

    if (baseDateOptions && isChanged) {
      const defaultBaseDate = getDefaultBaseDate(baseDateOptions);
      handleDateChange(defaultBaseDate);
      prevDeps.current = { department: dep, workHoursType: wht };
    }
  }, [baseDateOptions, department?.value, workHoursType?.value]);

  return (
    <div className="flex flex-wrap items-center justify-between">
      <div className="mr-4 flex flex-1 items-center">
        <FormField
          control={control}
          name="department"
          render={({ field }) => (
            <FormControl>
              <Select
                {...field}
                className={cn(SELECT_WIDTH)}
                isLoading={isGetScheduleDepartmentsLoading}
                isDisabled={isDisabled}
                isClearable={false}
                value={field.value}
                placeholder={t('請選擇部門')}
                options={departmentOptions}
                onChange={handleSelectChange('department')}
              />
            </FormControl>
          )}
        />
        <Separator orientation="vertical" className="mx-4 h-5" />
        <FormField
          control={control}
          name="workHoursType"
          render={({ field }) => (
            <FormControl>
              <Select
                {...field}
                className={cn(SELECT_WIDTH)}
                isClearable={false}
                isDisabled={isDisabled}
                value={field.value}
                options={workHoursTypeOptions}
                onChange={handleSelectChange('workHoursType')}
              />
            </FormControl>
          )}
        />
        {isBaseDateRequired(workHoursType?.value as WorkHoursTypeEnum) && (
          <>
            <span className="mx-2">{t('自')}</span>
            <FormField
              control={control}
              name="baseDate"
              render={({ field }) => (
                <FormControl>
                  <Select
                    {...field}
                    className="w-[138px] shrink-0"
                    isClearable={false}
                    isDisabled={isDisabled || isEmpty(field.value)}
                    value={baseDateOptions?.find(opt => opt.value === field.value) ?? null}
                    options={baseDateOptions}
                    onChange={handleDateChange}
                    dropdownIcon={<CalendarIcon size={16} />}
                  />
                </FormControl>
              )}
            />
          </>
        )}
      </div>
      {/* TODO 待後端提供資料 */}
      <div className="w-[160px] text-left text-sm text-[#587282]">
        {t('儲存於 2025/07/21 14:30')}
        <br />
        {t('發佈於 2025/07/21 14:30')}
      </div>
    </div>
  );
};

export default SearchBarFields;
