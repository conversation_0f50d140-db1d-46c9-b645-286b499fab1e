import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import {
  Employee,
  Employee as EmployeeApi,
  ShiftScheduleParams,
  useGetShiftSchedule,
} from '@/hooks/api/useGetShiftSchedule';
import { SearchBarForm } from '@/hooks/useSearchBarForm';
import { getDaysInRange } from '@/pages/ShiftScheduleApproval/utils';

import CalendarHeader from './CalendarHeader';
import DepartmentStats from './DepartmentStats';
import EmployeeRow from './EmployeeRow';

interface ScheduleCalendarProps {
  schedulingParams: ShiftScheduleParams;
}
const ScheduleCalendar: React.FC<ScheduleCalendarProps> = ({ schedulingParams }) => {
  const [selectedEmployee, setSelectedEmployee] = useState<EmployeeApi | undefined>();
  const { getValues } = useFormContext<SearchBarForm>();
  const {
    department,
    dateRange: { startDate, endDate },
  } = getValues();

  const { data } = useGetShiftSchedule({
    params: schedulingParams,
    enabled: !!schedulingParams,
    filter: selectedEmployee ? { employeeId: selectedEmployee.employeeId } : undefined,
  });

  const employees = data?.employees ?? [];
  const days = getDaysInRange(startDate, endDate);

  const handleSearch = (value: EmployeeApi | null) => {
    setSelectedEmployee(value ?? undefined);
  };

  return (
    <div className="mt-4 max-h-[600px] overflow-hidden">
      <div className="flex h-full flex-col">
        <div
          className="max-h-[600px] flex-1 overflow-auto rounded-lg border scrollbar-none"
          data-schedule-content
        >
          <div className="flex min-w-fit flex-col">
            <CalendarHeader days={days} employees={employees} handleSearch={handleSearch} />
            <DepartmentStats departmentName={department.label} days={days} employees={employees} />
            <div className="flex-1 divide-y">
              {employees.map((employee: Employee) => (
                <EmployeeRow key={employee.employeeNumber} employee={employee} days={days} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleCalendar;
