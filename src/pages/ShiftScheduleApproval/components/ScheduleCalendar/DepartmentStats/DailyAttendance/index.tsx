import { Employee } from '@/hooks/api/useGetShiftSchedule';
import { calendarStyles } from '@/pages/ShiftScheduleApproval/constants';
import { calculateDailyAttendanceCount } from '@/pages/ShiftScheduleApproval/utils/shift-business-logic';
import { cn } from '@/utils/style';

import StatsNumber from '../StatsNumber';

// 每日出勤統計組件
interface DailyAttendanceProps {
  date: string;
  employees: Employee[];
}

export const DailyAttendance: React.FC<DailyAttendanceProps> = ({ date, employees }) => {
  const totalCount = calculateDailyAttendanceCount(date, employees);

  return (
    <div
      className={cn(
        'flex flex-1 items-center justify-center text-center',
        calendarStyles.dayCellWidth,
      )}
    >
      <StatsNumber value={totalCount} className="text-blue" />
    </div>
  );
};
