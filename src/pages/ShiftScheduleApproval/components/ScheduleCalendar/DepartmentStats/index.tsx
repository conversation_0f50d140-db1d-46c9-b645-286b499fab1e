import { Employee } from '@/hooks/api/useGetShiftSchedule';
import { calendarStyles } from '@/pages/ShiftScheduleApproval/constants';
import { cn } from '@/utils/style';

import { DailyAttendance } from './DailyAttendance';

interface DepartmentStatsProps {
  departmentName: string;
  days: string[];
  employees: Employee[];
}
const DepartmentStats: React.FC<DepartmentStatsProps> = ({ departmentName, days, employees }) => {
  const title = (
    <div className="flex w-full items-center justify-between">
      <div className="text-base font-medium">{departmentName}</div>
      {/* <div className="rounded-l-md bg-blue p-1 text-xs text-white">統計</div> */}
    </div>
  );
  return (
    <div className="sticky top-[67px] z-[30] flex border-b border-t bg-dark-50">
      <StatsHeaderRow title={title} />
      {days.map(day => (
        <DailyAttendance key={day} date={day} employees={employees} />
      ))}
    </div>
  );
};

// 統計標題行組件
export const StatsHeaderRow: React.FC<{ title: React.ReactNode }> = ({ title }) => (
  <div
    className={cn(
      'sticky left-0 z-10 flex items-center justify-end border-r bg-[#f6f8f9] py-1 pl-5 text-sm font-medium',
      calendarStyles.stickyColumnWidth,
    )}
  >
    {title}
  </div>
);

export default DepartmentStats;
