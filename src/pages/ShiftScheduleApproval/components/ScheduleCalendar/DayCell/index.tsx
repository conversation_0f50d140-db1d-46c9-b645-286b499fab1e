import dayjs from 'dayjs';

import { Calendar, Employee } from '@/hooks/api/useGetShiftSchedule';
import { calendarStyles } from '@/pages/ShiftScheduleApproval/constants';
import {
  determineShiftStatus,
  getShiftRenderConditions,
} from '@/pages/ShiftScheduleApproval/utils/shift-business-logic';
import { cn } from '@/utils/style';

import {
  AdjustmentMark,
  DayOffBadge,
  HolidayBadge,
  LeaveBadge,
  MonthLeaveBadge,
  NoScheduleBadge,
  RestDayBadge,
  ShiftContainer,
  SupportBadge,
  TripBadge,
  UsualHolidayBadge,
  WorkDayShift,
} from './components';

/**
 * 日期單元格元件
 */
interface DayCellProps {
  index: number;
  day: string;
  employee: Employee;
}

const DayCell: React.FC<DayCellProps> = ({ index, day, employee }) => {
  const dayObj = dayjs(day);
  const isWeekend = dayObj.day() === 0 || dayObj.day() === 6;
  const isFirstColumn = index === 0;

  // 找到該員工該日期的行事曆資料
  const dayCalendar = employee.calendars.find(
    calendar => dayjs(calendar.date).format('YYYY-MM-DD') === day,
  );

  // 渲染有排班情況
  const renderHaveSchedule = (calendar: Calendar) => {
    const { shiftSchedule, tripSheets, leaveSheets } = calendar;

    if (!shiftSchedule) return null;

    const conditions = getShiftRenderConditions(calendar, shiftSchedule);

    // 1. 節假日特例
    if (conditions.isHolidaySpecial) {
      return <HolidayBadge />;
    }

    // 2. 無排班（事件在 3 or 4，且循環狀態為 2）
    if (conditions.isNoScheduleWithVacationCycle) {
      return;
    }

    // 3. 其餘條件下的排班組件
    const nodes: React.ReactNode[] = [];

    // 午／夜班（cycleStatus !== 2）
    if (conditions.shouldShowWorkDay) {
      nodes.push(<WorkDayShift key="work-day" shiftSchedule={shiftSchedule} />);
    }

    // 補休
    if (conditions.shouldShowDayOff) {
      nodes.push(<DayOffBadge key="day-off" />);
    }

    // 全月請假
    if (conditions.shouldShowMonthLeave) {
      nodes.push(<MonthLeaveBadge key="month-leave" />);
    }

    // 休息日
    if (conditions.shouldShowRestDay) {
      nodes.push(<RestDayBadge key="rest-day" />);
    }

    // 一般假期
    if (conditions.shouldShowUsualHoliday) {
      nodes.push(<UsualHolidayBadge key="usual-holiday" />);
    }

    // 調班標記
    if (conditions.shouldShowAdjustmentMark) {
      nodes.push(<AdjustmentMark key="adjustment" />);
    }

    // 請假單
    if (leaveSheets && leaveSheets.length > 0) {
      nodes.push(<LeaveBadge key="leave" />);
    }

    // 出差
    if (tripSheets && tripSheets.length > 0) {
      nodes.push(<TripBadge key="trip" />);
    }

    return <ShiftContainer>{nodes}</ShiftContainer>;
  };

  // 渲染邏輯
  const renderCellContent = () => {
    const shiftStatus = determineShiftStatus(dayCalendar);

    if (shiftStatus.isHaveSchedule) {
      return renderHaveSchedule(dayCalendar!);
    }

    if (shiftStatus.isSupportSchedule) {
      return <SupportBadge />;
    }

    if (shiftStatus.isNoSchedule) {
      return <NoScheduleBadge />;
    }

    if (shiftStatus.isHoliday) {
      return <HolidayBadge />;
    }

    if (shiftStatus.isDayOff) {
      return <DayOffBadge />;
    }

    if (shiftStatus.isMonthLeave) {
      return <MonthLeaveBadge />;
    }

    if (shiftStatus.isRestDay) {
      return <RestDayBadge />;
    }

    if (shiftStatus.isUsualHoliday) {
      return <UsualHolidayBadge />;
    }

    return null;
  };

  return (
    <div
      className={cn('flex-1 p-0.5', calendarStyles.dayCellWidth, {
        'bg-dark-50': isWeekend,
        '-ml-[1px]': isFirstColumn,
      })}
      data-employee-id={employee.employeeNumber}
      data-date={day}
    >
      <div className="flex flex-col gap-0.5">{renderCellContent()}</div>
    </div>
  );
};

export default DayCell;
