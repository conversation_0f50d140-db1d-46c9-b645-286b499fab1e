import React from 'react';

import ShiftBadge from '@/components/ShiftBadge';
import { ShiftScheduleDetail } from '@/hooks/api/useGetShiftSchedule';

// 工作日班次組件
interface WorkDayShiftProps {
  shiftSchedule: ShiftScheduleDetail;
}

// 工具函數：獲取班次顯示顏色
export const getShiftColor = (shiftSchedule: ShiftScheduleDetail | null): string => {
  return shiftSchedule?.colorCode || '#99999B';
};

// 工具函數：獲取班次顯示名稱
export const getShiftTitle = (shiftSchedule: ShiftScheduleDetail | null): string => {
  return shiftSchedule?.shiftScheduleName || '未排班';
};

export const WorkDayShift: React.FC<WorkDayShiftProps> = ({ shiftSchedule }) => {
  const color = getShiftColor(shiftSchedule);
  const title = getShiftTitle(shiftSchedule);

  return (
    <ShiftBadge
      colorCode={color}
      title={title}
      startTime={shiftSchedule.workOnTime as string}
      endTime={shiftSchedule.workOffTime as string}
    />
  );
};

// 補休標籤
export const DayOffBadge: React.FC = () => <ShiftBadge colorCode="#007AFF" title="補休" />;

// 全月請假標籤
export const MonthLeaveBadge: React.FC = () => <ShiftBadge colorCode="#9059FF" title="月休" />;

// 休息日標籤
export const RestDayBadge: React.FC = () => <ShiftBadge colorCode="#e47373" title="休息日" />;

// 一般假期標籤
export const UsualHolidayBadge: React.FC = () => <ShiftBadge colorCode="#e0663e" title="例假日" />;

// 請假標籤
export const LeaveBadge: React.FC = () => <ShiftBadge colorCode="#FF9500" title="請假" />;

// 出差標籤
export const TripBadge: React.FC = () => <ShiftBadge colorCode="#6A5ACD" title="出差" />;

// 支援部門標籤
export const SupportBadge: React.FC = () => <ShiftBadge colorCode="#FFD700" title="支援" />;

// 無排班標籤
export const NoScheduleBadge: React.FC = () => <></>;

// 節假日標籤
export const HolidayBadge: React.FC = () => <ShiftBadge colorCode="#f27c2a" title="國定假日" />;

// 調班標記
export const AdjustmentMark: React.FC = () => <div className="mt-1 h-0.5 w-full bg-blue-500" />;

// 班次容器組件
interface ShiftContainerProps {
  children: React.ReactNode;
}

export const ShiftContainer: React.FC<ShiftContainerProps> = ({ children }) => (
  <div className="relative flex flex-col gap-0.5">{children}</div>
);
