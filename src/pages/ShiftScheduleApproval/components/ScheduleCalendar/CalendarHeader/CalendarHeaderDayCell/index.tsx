import clsx from 'clsx';
import dayjs from 'dayjs';

import { calendarStyles } from '@/pages/ShiftScheduleApproval/constants';
import { cn } from '@/utils/style';

interface CalendarHeaderDayCellProps {
  day: string;
  index: number;
  weekdayMap: Record<number, string>;
}

const DAY_CELL_MARGIN = 'ml-3';

const CalendarHeaderDayCell: React.FC<CalendarHeaderDayCellProps> = ({
  day,
  index,
  weekdayMap,
}) => {
  const dayObj = dayjs(day);
  const isFirstColumn = index === 0;
  const isFirstDayOfMonth = dayjs(day).date() === 1;
  const showMonthTitle = isFirstColumn || isFirstDayOfMonth;
  const isToday = dayObj.isSame(dayjs(), 'day');

  return (
    <div
      key={day}
      className={cn(
        'flex flex-1 flex-col bg-white py-2',
        calendarStyles.dayCellWidth,
        showMonthTitle ? 'justify-between border-l' : 'justify-end',
        isFirstColumn && '-ml-[1px]',
      )}
      role="columnheader"
      aria-colindex={index + 2}
      data-date={day}
    >
      {showMonthTitle && (
        <div className={clsx('text-left text-xs font-medium text-blue', DAY_CELL_MARGIN)}>
          {dayObj.format('YYYY 年 MM 月')}
        </div>
      )}
      <div
        className={cn(
          'relative flex w-full items-center justify-center gap-4',
          index !== 0 && !showMonthTitle && 'border-l',
        )}
      >
        <div
          className={cn(
            'text-xs text-dark-300',
            'absolute left-0 top-1/2 -translate-y-1/2',
            DAY_CELL_MARGIN,
          )}
        >
          {weekdayMap[dayObj.day()]}
        </div>
        <div
          className={cn(
            'text-[18px] font-medium',
            isToday && 'flex size-8 items-center justify-center rounded-lg bg-primary text-white',
          )}
        >
          {dayObj.date()}
        </div>
      </div>
    </div>
  );
};

export default CalendarHeaderDayCell;
