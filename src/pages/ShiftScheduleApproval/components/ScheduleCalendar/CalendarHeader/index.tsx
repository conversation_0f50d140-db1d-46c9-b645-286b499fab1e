import { Select } from '@mayo/mayo-ui-beta/v2';
import { Search } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Employee as EmployeeApi } from '@/hooks/api/useGetShiftSchedule';
import { calendarStyles } from '@/pages/ShiftScheduleApproval/constants';
import { getWeekdayMap } from '@/pages/ShiftScheduleApproval/utils';
import { cn } from '@/utils/style';

import CalendarHeaderDayCell from './CalendarHeaderDayCell';

interface CalendarHeaderProps {
  days: string[];
  employees: Employee<PERSON><PERSON>[];
  handleSearch: (value: EmployeeApi | null) => void;
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({ days, employees, handleSearch }) => {
  const { t } = useTranslation();
  const weekdayMap = getWeekdayMap(t);

  return (
    <div className="sticky top-0 z-[30] bg-white" role="row" aria-rowindex={1}>
      <div className="flex">
        <div
          className={cn(
            'sticky left-0 z-[10] border-r bg-white p-4',
            calendarStyles.stickyColumnWidth,
          )}
          role="columnheader"
          aria-colindex={1}
        >
          <Select
            className="w-[208px]"
            isDisabled={false}
            isClearable={true}
            dropdownIcon={<Search size={16} />}
            placeholder={t('輸入關鍵字搜尋')}
            options={employees}
            getOptionValue={(option: EmployeeApi) => option.employeeId}
            getOptionLabel={(option: EmployeeApi) =>
              // TODO 等後端改 API 加上職稱
              `${option.chineseName} / ${option.employeeNumber}`
            }
            onChange={handleSearch}
          />
        </div>
        {/* 日期和星期行 */}
        {days.map((day, index) => (
          <CalendarHeaderDayCell key={day} day={day} index={index} weekdayMap={weekdayMap} />
        ))}
      </div>
    </div>
  );
};

export default CalendarHeader;
