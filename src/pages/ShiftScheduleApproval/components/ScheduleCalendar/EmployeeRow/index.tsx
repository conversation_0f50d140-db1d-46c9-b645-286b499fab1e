import { Avatar, Badge } from '@mayo/mayo-ui-beta/v2';
import { useTranslation } from 'react-i18next';

import UserAvatarImg from '@/assets/images/<EMAIL>';
import { Employee } from '@/hooks/api/useGetShiftSchedule';
import { calendarStyles } from '@/pages/ShiftScheduleApproval/constants';
import { cn } from '@/utils/style';

import DayCell from '../DayCell';
/**
 * 員工行元件
 */
interface EmployeeRowProps {
  employee: Employee;
  days: string[];
}

interface EmployeeCardProps {
  avatarUrl?: string;
  name: string;
  employeeNumber: string;
  position?: string;
  isSupport?: boolean;
}

const EmployeeCard: React.FC<EmployeeCardProps> = ({
  avatarUrl,
  name,
  employeeNumber,
  position,
  isSupport,
}) => {
  const avatar = avatarUrl || UserAvatarImg;
  const { t } = useTranslation();

  return (
    <div className="flex flex-1 items-center justify-between px-5 py-2">
      <div className="flex items-center gap-3">
        <div>
          <Avatar size="sm" alt="employee" src={avatar} />
        </div>
        <div>
          <p className="text-base font-medium">{name}</p>
          <p className="line-clamp-1 text-sm text-[#587282]">
            {employeeNumber}
            {!isSupport && position && (
              <span className="text-sm text-[#587282]">{` / ${position}`}</span>
            )}
          </p>
        </div>
      </div>
      {isSupport && (
        <Badge className="" color="green">
          {t('支援')}
        </Badge>
      )}
    </div>
  );
};

const EmployeeRow: React.FC<EmployeeRowProps> = ({ employee, days }) => {
  return (
    <div className="flex divide-x">
      <div
        className={cn(
          'sticky left-0 z-[10] flex items-center border-r bg-white',
          calendarStyles.stickyColumnWidth,
        )}
      >
        <EmployeeCard
          key={employee.employeeId}
          name={employee.chineseName}
          employeeNumber={employee.employeeNumber}
          // TODO 待後端 API 提供
          // avatarUrl={employee.avatar}
          // position={employee.position}
        />
      </div>

      {days.map((day, index) => (
        <DayCell key={day} index={index} day={day} employee={employee} />
      ))}
    </div>
  );
};

export default EmployeeRow;
