import { Select } from '@mayo/mayo-ui-beta/v2';
import { CalendarIcon } from 'lucide-react';
import { useMemo } from 'react';
import { FieldValues } from 'react-hook-form';

import { SelectOption } from '@/types/interface/select';

// 純函數：查找當前選中的選項
const findCurrentOption = (
  fieldValue: { startDate: string; endDate: string } | null,
  options: SelectOption<[string, string]>[],
): SelectOption<[string, string]> | null => {
  if (!fieldValue) return null;

  return (
    options.find(
      opt => opt.value[0] === fieldValue.startDate && opt.value[1] === fieldValue.endDate,
    ) || null
  );
};

// 純函數：創建日期範圍對象
const createDateRangeFromOption = (option: SelectOption<[string, string]>) => {
  return {
    startDate: option.value[0],
    endDate: option.value[1],
  };
};

interface FlexibleHourDatePickerProps extends FieldValues {
  options: SelectOption<[string, string]>[];
  placeholder: string;
  isDisabled?: boolean;
  isLoading?: boolean;
  onChange?: (dateRange: { startDate: string; endDate: string }) => void;
}

export const FlexibleHourDatePicker: React.FC<FlexibleHourDatePickerProps> = ({
  field,
  options,
  placeholder,
  isDisabled,
  isLoading,
  onChange: onDateRangeChange,
}) => {
  const currentValue = useMemo(() => {
    return findCurrentOption(field.value, options);
  }, [field.value, options]);

  const handleSelectChange = (option: SelectOption<[string, string]>) => {
    if (!option) return;

    const dateRange = createDateRangeFromOption(option);
    field.onChange(dateRange);
    onDateRangeChange?.(dateRange);
  };

  return (
    <Select
      isLoading={isLoading}
      isDisabled={isDisabled}
      isClearable={false}
      dropdownIcon={<CalendarIcon size={16} />}
      placeholder={placeholder}
      value={currentValue}
      options={options}
      onChange={handleSelectChange}
    />
  );
};
