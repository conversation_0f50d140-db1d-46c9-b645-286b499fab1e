import { FormControl, FormField } from '@mayo/mayo-ui-beta/v2';
import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { useDateRangeField } from '@/hooks/useDateRangeField/indext';
import { useDateRangeNavigation } from '@/hooks/useDateRangeNavigation/indext';
import { SearchBarForm } from '@/hooks/useSearchBarForm';

import { FlexibleHourDatePicker } from './FlexibleHourDatePicker';
import NavigationButton from './NavigationButton';
import { StandardHourDatePicker } from './StandardHourDatePicker';

interface DateRangeFieldProps {
  isLoading: boolean;
}

const DateRangeField: React.FC<DateRangeFieldProps> = ({ isLoading }) => {
  const { t } = useTranslation();

  const {
    control,
    getValues,
    formState: { isSubmitting, isLoading: isFormLoading },
    setValue,
  } = useFormContext<SearchBarForm>();

  const onDateRangeChange = useCallback(
    (dateRange: { startDate: string; endDate: string }) => {
      setValue('dateRange', dateRange);
    },
    [setValue],
  );

  const { options, isStandardWorkHour } = useDateRangeField({ onDateRangeChange });
  const { handlePrev, handleNext } = useDateRangeNavigation({
    mode: isStandardWorkHour ? 'month' : 'flexible',
    value: getValues('dateRange'),
    options,
    onChange: onDateRangeChange,
  });
  const isDisabled = isLoading || isFormLoading || isSubmitting;

  return (
    <FormField
      control={control}
      name="dateRange"
      render={({ field }) => (
        <FormControl>
          <div className="flex items-center gap-2">
            <NavigationButton direction="prev" disabled={isDisabled} onClick={handlePrev} />

            <div className="w-[220px] shrink-0">
              {isStandardWorkHour ? (
                <StandardHourDatePicker
                  field={field}
                  isDisabled={isDisabled}
                  onChange={onDateRangeChange}
                />
              ) : (
                <FlexibleHourDatePicker
                  field={field}
                  isLoading={isDisabled}
                  isDisabled={isDisabled}
                  options={options}
                  placeholder={t('請選擇日期區間')}
                  onChange={onDateRangeChange}
                />
              )}
            </div>

            <NavigationButton direction="next" disabled={isDisabled} onClick={handleNext} />
          </div>
        </FormControl>
      )}
    />
  );
};

export default DateRangeField;
