import { DatePicker } from '@mayo/mayo-ui-beta/v2';
import dayjs from 'dayjs';
import i18next from 'i18next';
import { FieldValues } from 'react-hook-form';

// 純函數：格式化月份日期
const formatMonthDate = (dateString: string): string => {
  return dayjs(dateString).format('YYYY/MM');
};

// 純函數：創建月份日期範圍
const createMonthDateRange = (dateString: string) => {
  const startDate = dayjs(dateString).startOf('month').format('YYYY-MM-DD');
  const endDate = dayjs(dateString).endOf('month').format('YYYY-MM-DD');
  return { startDate, endDate };
};

interface StandardHourDatePickerProps extends FieldValues {
  isDisabled: boolean;
  onChange?: (dateRange: { startDate: string; endDate: string }) => void;
}

export const StandardHourDatePicker: React.FC<StandardHourDatePickerProps> = ({
  field: { value, onChange },
  isDisabled,
  onChange: onDateRangeChange,
}) => {
  const currentLanguage = i18next.language;
  if (!value) return null;

  const handleDateChange = (dateString: string) => {
    const dateRange = createMonthDateRange(dateString);
    onChange(dateRange);
    onDateRangeChange?.(dateRange);
  };

  return (
    <DatePicker
      picker="month"
      language={currentLanguage as 'zh-CN' | 'zh-TW' | 'en-US' | 'VN'}
      format="YYYY/MM"
      value={formatMonthDate(value.startDate)}
      disabled={isDisabled}
      allowClear={false}
      onChange={handleDateChange}
    />
  );
};
