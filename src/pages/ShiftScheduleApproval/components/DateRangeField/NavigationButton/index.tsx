import { Button } from '@mayo/mayo-ui-beta/v2';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const NavigationButton: React.FC<{
  direction: 'prev' | 'next';
  disabled: boolean;
  onClick: () => void;
}> = ({ direction, disabled, onClick }) => {
  const Icon = direction === 'prev' ? ChevronLeft : ChevronRight;

  return (
    <Button
      variant="outline"
      size="icon"
      color="secondary"
      disabled={disabled}
      className="flex size-[35px] items-center justify-center border border-gray-200 bg-white"
      onClick={onClick}
    >
      <Icon size={16} />
    </Button>
  );
};
export default NavigationButton;
