import { Button, CompoundDropdownMenu, CompoundDropdownMenuItem } from '@mayo/mayo-ui-beta/v2';
import { Ellipsis } from 'lucide-react';

interface MoreButtonProps {
  menuItems: CompoundDropdownMenuItem[];
}

const MoreButton = ({ menuItems }: MoreButtonProps) => {
  if (menuItems.length === 0) return null;

  return (
    <CompoundDropdownMenu
      menuItems={menuItems}
      placement="bottom-end"
      trigger={
        <Button
          variant="outline"
          size="icon"
          color="secondary"
          className="flex size-9 items-center justify-center rounded-md border border-gray-200 bg-white"
        >
          <Ellipsis size={16} />
        </Button>
      }
    />
  );
};

export default MoreButton;
