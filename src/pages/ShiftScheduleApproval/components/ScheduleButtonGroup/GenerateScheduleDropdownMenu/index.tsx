import { Button, CompoundDropdownMenu } from '@mayo/mayo-ui-beta/v2';
import { ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { BUTTON_IDS, ScheduleButtonConfig } from '..';

interface GenerateScheduleDropdownMenuProps {
  buttons: ScheduleButtonConfig[];
}

export const GenerateScheduleDropdownMenu: React.FC<GenerateScheduleDropdownMenuProps> = ({
  buttons,
}) => {
  const { t } = useTranslation();

  const generateButton = buttons.find(button => button.id === BUTTON_IDS.GENERATE);
  const menuItems = generateButton?.type === 'sub' ? generateButton.subItems || [] : [];

  return (
    <CompoundDropdownMenu
      menuItems={menuItems}
      placement="bottom-end"
      trigger={
        <Button>
          {t('產生班表')}
          <ChevronDown size={16} className="ml-2" />
        </Button>
      }
    />
  );
};

export default GenerateScheduleDropdownMenu;
