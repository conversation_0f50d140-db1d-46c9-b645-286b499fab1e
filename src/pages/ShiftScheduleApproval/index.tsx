import { Form, Loading, Select } from '@mayo/mayo-ui-beta/v2';
import { isEmpty } from 'lodash';
import { useMemo } from 'react';
import { FormProvider } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import EmptyDataImage from '@/assets/images/shiftScheduleApproval_empty-data.svg';
import { BreadcrumbItem, Breadcrumbs } from '@/components/common/Breadcrumbs';
import { useGetScheduleDepartments } from '@/hooks/api/useGetScheduleDepartments';
import { useGetShiftSchedule } from '@/hooks/api/useGetShiftSchedule';
import { useSearchBarForm } from '@/hooks/useSearchBarForm';
import { WorkHoursTypeEnum } from '@/types/enums/shiftScheduleApproval';
import { SelectOption } from '@/types/interface/select';

import DateRangeField from './components/DateRangeField';
import ScheduleButtonGroup from './components/ScheduleButtonGroup';
import ScheduleCalendar from './components/ScheduleCalendar';
import SearchBarFields from './components/SearchBarFields';

// TODO 未來可改成從後端資料組成 breadcrumbItems
const breadcrumbItems: BreadcrumbItem[] = [
  { label: '管理專區', href: '' },
  { label: '班表管理', href: '' },
  { label: '班表審核', href: '/shift-schedule-approval', isCurrent: true },
];

const ShiftScheduleApprovalPage: React.FC = () => {
  const { t } = useTranslation();

  // API: 取得部門資料
  const { data: departmentsData, isLoading: isDepartmentsLoading } = useGetScheduleDepartments();

  // 部門選項
  const departmentOptions = useMemo(
    () =>
      departmentsData?.map(dep => ({
        label: dep.deptCName ?? '',
        value: dep.departmentId,
      })) ?? [],
    [departmentsData],
  );
  // 工時制選項
  const workHoursTypeOptions: SelectOption<WorkHoursTypeEnum>[] = [
    {
      label: t('標準工時制'),
      value: WorkHoursTypeEnum.STANDARD,
    },
    {
      label: t('雙週變形工時'),
      value: WorkHoursTypeEnum.TWO_WEEKS,
    },
    {
      label: t('四週變形工時'),
      value: WorkHoursTypeEnum.FOUR_WEEKS,
    },
    {
      label: t('八週變形工時'),
      value: WorkHoursTypeEnum.EIGHT_WEEKS,
    },
  ];

  // 搜尋班表 form 組件
  const {
    form: searchBarForm,
    isFormReady,
    schedulingParams,
  } = useSearchBarForm({
    departmentOptions,
    workHoursTypeOptions,
  });

  // API: 取得班表資料
  const { data: shiftScheduleData, isLoading: isShiftScheduleLoading } = useGetShiftSchedule({
    params: schedulingParams,
    enabled: !!schedulingParams,
  });

  const hasNoData = isEmpty(shiftScheduleData?.employees);
  const isLoading = !isFormReady || isDepartmentsLoading || isShiftScheduleLoading;

  // Debug 資訊
  console.log('🔍 Debug Info:', {
    schedulingParams,
    isFormReady,
    formIsValid: searchBarForm.formState.isValid,
    formValues: searchBarForm.getValues(),
    departmentOptions: departmentOptions,
    workHoursTypeOptions: workHoursTypeOptions,
    shiftScheduleData: shiftScheduleData,
    isShiftScheduleLoading: isShiftScheduleLoading,
    hasNoData: hasNoData,
  });

  const renderScheduleContent = () => {
    if (isLoading || !schedulingParams) {
      return (
        <div className="absolute left-[50%] top-[50%] -translate-x-1/2 -translate-y-1/2 transform">
          <Loading />
        </div>
      );
    }
    if (hasNoData) {
      return (
        <div className="absolute left-[50%] top-[50%] -translate-x-1/2 -translate-y-1/2 transform">
          <img src={EmptyDataImage} alt="empty-data" />
          <p className="mt-5 text-center text-base">{t('無需審核班表')}</p>
        </div>
      );
    }
    return <ScheduleCalendar schedulingParams={schedulingParams} />;
  };

  return (
    <div>
      <Breadcrumbs items={breadcrumbItems} />
      <h1 className="mb-4 mt-2 text-h2 font-medium">{t('班表審核')}</h1>

      <FormProvider {...searchBarForm}>
        <Form {...searchBarForm}>
          <form onSubmit={searchBarForm.handleSubmit(() => {})}>
            <SearchBarFields
              departmentOptions={departmentOptions}
              workHoursTypeOptions={workHoursTypeOptions}
            />

            <div className="relative mt-4 h-[calc(100dvh-56px-116px-48px)] overflow-hidden rounded-2xl bg-white p-5">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Select
                    className="mr-4 w-[160px] shrink-0"
                    isDisabled={true}
                    isClearable={false}
                    isSearchable={false}
                    // 目前只有人員列表，先寫死 options & value
                    options={[
                      { label: t('人員列表'), value: 'peopleList' },
                      { label: t('班次列表'), value: 'shiftScheduleList' },
                    ]}
                    value={[{ label: t('人員列表'), value: 'peopleList' }]}
                  />
                  <DateRangeField isLoading={isLoading} />
                </div>
                {!hasNoData && <ScheduleButtonGroup />}
              </div>

              {/* 班表行事曆 */}
              {schedulingParams && renderScheduleContent()}
            </div>
          </form>
        </Form>
      </FormProvider>
    </div>
  );
};

export default ShiftScheduleApprovalPage;
