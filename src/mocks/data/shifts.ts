import { faker } from '../config/faker';
import { Shift, ShiftType } from '../types';

// 預設班次類型
export const mockShiftTypes: ShiftType[] = [
  {
    id: 'shift_type_1',
    title: '早班',
    startTime: '09:00',
    endTime: '17:00',
    color: '#4F46E5',
    isActive: true,
  },
  {
    id: 'shift_type_2',
    title: '晚班',
    startTime: '17:00',
    endTime: '01:00',
    color: '#7C3AED',
    isActive: true,
  },
  {
    id: 'shift_type_3',
    title: '夜班',
    startTime: '01:00',
    endTime: '09:00',
    color: '#1F2937',
    isActive: true,
  },
  {
    id: 'shift_type_4',
    title: '全日班',
    startTime: '09:00',
    endTime: '21:00',
    color: '#059669',
    isActive: true,
  },
];

// 可配置的班次數量
const SHIFT_COUNT = parseInt((import.meta.env?.VITE_MOCK_SHIFT_COUNT as string) ?? '100');

// 班次狀態選項
const shiftStatuses: Shift['status'][] = ['scheduled', 'confirmed', 'completed', 'cancelled'];

// 生成班次資料的函數
function generateShift(index: number): Shift {
  const shiftType = faker.helpers.arrayElement(mockShiftTypes);
  const date = faker.date.between({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
    to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天後
  });

  // 解析班次時間
  const [startHour, startMinute] = shiftType.startTime.split(':').map(Number);
  const [endHour, endMinute] = shiftType.endTime.split(':').map(Number);

  // 計算當日時間戳
  const startTime = new Date(date);
  startTime.setHours(startHour, startMinute, 0, 0);

  const endTime = new Date(date);
  endTime.setHours(endHour, endMinute, 0, 0);

  // 如果結束時間小於開始時間，表示跨日
  if (endTime < startTime) {
    endTime.setDate(endTime.getDate() + 1);
  }

  return {
    id: String(index + 1),
    employeeId: String(faker.number.int({ min: 1, max: 20 })), // 對應員工ID
    storeId: String(faker.number.int({ min: 1, max: 15 })), // 對應門店ID
    shiftTypeId: shiftType.id,
    date: date.toISOString().split('T')[0], // YYYY-MM-DD 格式
    startTime: startTime.getTime(),
    endTime: endTime.getTime(),
    status: faker.helpers.arrayElement(shiftStatuses),
    notes: faker.datatype.boolean(0.3) ? faker.lorem.sentence() : undefined, // 30% 機率有備註
    createdAt: faker.date.past({ years: 1 }).toISOString(),
    updatedAt: faker.date.recent({ days: 14 }).toISOString(),
  };
}

// 生成班次列表
export const mockShifts: Shift[] = Array.from({ length: SHIFT_COUNT }, (_, index) =>
  generateShift(index),
);

// 重置 faker seed
faker.seed();
