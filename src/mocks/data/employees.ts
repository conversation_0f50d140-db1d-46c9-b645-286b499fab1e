import { faker, taiwanFaker } from '../config/faker';
import { Employee } from '../types';

// 可配置的員工數量
const EMPLOYEE_COUNT = parseInt((import.meta.env?.VITE_MOCK_EMPLOYEE_COUNT as string) ?? '20');

// 預設顏色陣列
const colors = [
  '#4F46E5',
  '#0EA5E9',
  '#10B981',
  '#F59E0B',
  '#6366F1',
  '#EC4899',
  '#8B5CF6',
  '#14B8A6',
  '#F97316',
  '#EF4444',
  '#84CC16',
  '#06B6D4',
  '#8B5A2B',
  '#DC2626',
  '#7C3AED',
  '#059669',
  '#D97706',
  '#DC2626',
  '#7C2D12',
  '#1E40AF',
];

// 生成員工資料的函數
function generateEmployee(index: number): Employee {
  const department: string = taiwanFaker.department();
  const position: string = taiwanFaker.position(department);

  return {
    id: String(index + 1),
    name: taiwanFaker.name(),
    department,
    position,
    color: faker.helpers.arrayElement(colors) as string,
    isActive: faker.datatype.boolean(0.9), // 90% 機率為啟用狀態
    createdAt: faker.date.past({ years: 2 }).toISOString(),
    updatedAt: faker.date.recent({ days: 30 }).toISOString(),
  };
}

// 生成員工列表
export const mockEmployees: Employee[] = Array.from({ length: EMPLOYEE_COUNT }, (_, index) =>
  generateEmployee(index),
);

// 重置 faker seed 以確保其他模組的隨機性
faker.seed();
