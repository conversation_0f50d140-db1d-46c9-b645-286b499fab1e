import { faker, taiwanFaker } from '../config/faker';
import { Store } from '../types';

// 可配置的門店數量
const STORE_COUNT = parseInt((import.meta.env?.VITE_MOCK_STORE_COUNT as string) ?? '15');

// 生成門店資料的函數
function generateStore(index: number): Store {
  return {
    id: String(index + 1),
    name: taiwanFaker.storeName(),
    location: taiwanFaker.address(),
    isActive: faker.datatype.boolean(0.95), // 95% 機率為啟用狀態
    createdAt: faker.date.past({ years: 3 }).toISOString(),
    updatedAt: faker.date.recent({ days: 7 }).toISOString(),
  };
}

// 生成門店列表
export const mockStores: Store[] = Array.from({ length: STORE_COUNT }, (_, index) =>
  generateStore(index),
);

// 重置 faker seed
faker.seed();
