import { setupWorker } from 'msw/browser';

import { handlers } from './handlers';

// 創建瀏覽器環境的 MSW worker
export const worker = setupWorker(...handlers);

// 開發環境配置
const startWorkerConfig = {
  // 在控制台顯示攔截的請求
  onUnhandledRequest: 'warn' as const,

  // Service Worker 文件位置
  serviceWorker: {
    url: '/mockServiceWorker.js',
  },

  // 靜默模式 (生產環境建議開啟)
  quiet: false,
};

// 啟動 MSW
export const startMSW = async () => {
  if (typeof window !== 'undefined') {
    try {
      await worker.start(startWorkerConfig);
      console.log('🚀 MSW (Mock Service Worker) 已啟動');
      console.log('📡 可用的 API endpoints:');
      console.log('  - GET    /api/employees');
      console.log('  - POST   /api/employees');
      console.log('  - PUT    /api/employees/:id');
      console.log('  - DELETE /api/employees/:id');
      console.log('  - GET    /api/stores');
      console.log('  - GET    /api/shifts');
      console.log('  - POST   /api/shifts');
      console.log('  - PUT    /api/shifts/:id');
      console.log('  - DELETE /api/shifts/:id');
      console.log('  - GET    /api/shift-types');
      console.log('  - GET    /api/dashboard/stats');
    } catch (error) {
      console.error('❌ MSW 啟動失敗:', error);
    }
  }
};

// 停止 MSW
export const stopMSW = () => {
  worker.stop();
  console.log('🛑 MSW 已停止');
};
