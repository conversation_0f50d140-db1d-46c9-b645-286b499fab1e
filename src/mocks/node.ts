import { setupServer } from 'msw/node';

import { handlers } from './handlers';

// 創建 Node.js 環境的 MSW server
export const server = setupServer(...handlers);

// 測試環境配置
export const setupMSWForTesting = () => {
  // 在所有測試之前啟動 server
  // @ts-expect-error: Jest/Vitest global
  beforeAll(() => {
    server.listen({
      onUnhandledRequest: 'error',
    });
  });

  // 每個測試之後重置 handlers
  // @ts-expect-error: Jest/Vitest global
  afterEach(() => {
    server.resetHandlers();
  });

  // 所有測試之後關閉 server
  // @ts-expect-error: Jest/Vitest global
  afterAll(() => {
    server.close();
  });
};
