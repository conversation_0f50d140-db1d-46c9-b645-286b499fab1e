// MSW API 相關類型定義

export interface Employee {
  id: string;
  name: string;
  department: string;
  position: string;
  color: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Store {
  id: string;
  name: string;
  location: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ShiftType {
  id: string;
  title: string;
  startTime: string; // HH:mm 格式
  endTime: string; // HH:mm 格式
  color: string;
  isActive: boolean;
}

export interface Shift {
  id: string;
  employeeId: string;
  storeId: string;
  shiftTypeId: string;
  date: string; // YYYY-MM-DD 格式
  startTime: number; // timestamp
  endTime: number; // timestamp
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// API 響應格式
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  timestamp: string;
}

// API 請求參數
export interface EmployeeCreateRequest {
  name: string;
  department: string;
  position: string;
  color?: string;
}

export interface EmployeeUpdateRequest extends Partial<EmployeeCreateRequest> {
  isActive?: boolean;
}

export interface ShiftCreateRequest {
  employeeId: string;
  storeId: string;
  shiftTypeId: string;
  date: string;
  notes?: string;
}

export interface ShiftUpdateRequest extends Partial<ShiftCreateRequest> {
  status?: Shift['status'];
}

export interface ShiftQueryParams {
  employeeId?: string;
  storeId?: string;
  startDate?: string;
  endDate?: string;
  status?: Shift['status'];
  page?: number;
  limit?: number;
}
