import { faker } from '@faker-js/faker';

// 設置中文本地化 (新版 faker 不再有 locale 屬性)
// 使用 setLocale 或直接使用中文資料

// 開發環境使用固定 seed 確保資料一致性
// 生產環境可以設置為隨機
const DEVELOPMENT_SEED = 12345;
const isDevelopment = import.meta.env?.NODE_ENV === 'development';

if (isDevelopment) {
  faker.seed(DEVELOPMENT_SEED);
}

// 台灣本地化資料
export const taiwanData = {
  // 台灣常見姓氏
  lastNames: [
    '陳',
    '林',
    '黃',
    '張',
    '李',
    '王',
    '吳',
    '劉',
    '蔡',
    '楊',
    '許',
    '鄭',
    '謝',
    '郭',
    '洪',
    '邱',
    '曾',
    '廖',
    '賴',
    '徐',
    '周',
    '葉',
    '蘇',
    '莊',
    '呂',
    '江',
    '何',
    '蕭',
    '羅',
    '高',
  ],

  // 台灣常見名字
  firstNames: [
    '志明',
    '佳慧',
    '淑芬',
    '雅婷',
    '怡君',
    '志豪',
    '建宏',
    '美玲',
    '宗翰',
    '雅惠',
    '家豪',
    '靜宜',
    '俊傑',
    '怡珊',
    '承翰',
    '詩涵',
    '志偉',
    '麗娟',
    '文雄',
    '淑君',
    '國龍',
    '美惠',
    '俊宏',
    '雅芳',
    '宏達',
    '秀英',
    '建成',
    '文娟',
    '志宏',
    '淑華',
  ],

  // 部門列表 (餐飲業)
  departments: ['廚房部', '外場服務部', '吧台部', '管理部', '採購部', '清潔部', '人資部', '財務部'],

  // 職位列表 (餐飲業)
  positions: {
    廚房部: ['主廚', '副主廚', '廚師', '助理廚師', '備料員', '洗碗員'],
    外場服務部: ['店長', '領班', '服務員', '接待員', '收銀員'],
    吧台部: ['調酒師', '咖啡師', '飲品師', '吧台助理'],
    管理部: ['總經理', '副總經理', '營運經理', '區域經理'],
    採購部: ['採購主管', '採購專員', '供應商管理', '庫存管理員'],
    清潔部: ['清潔主管', '清潔員', '環境維護員'],
    人資部: ['人資主管', '人資專員', '招募專員', '培訓專員'],
    財務部: ['財務主管', '會計', '出納', '成本控制員'],
  } as Record<string, string[]>,

  // 台北區域
  taipeiAreas: [
    '中正區',
    '大同區',
    '中山區',
    '松山區',
    '大安區',
    '萬華區',
    '信義區',
    '士林區',
    '北投區',
    '內湖區',
    '南港區',
    '文山區',
  ],

  // 門店類型後綴
  storeTypes: ['旗艦店', '門市', '店', '分店', '專賣店', '概念店'],
};

// 自訂生成器
export const taiwanFaker = {
  // 生成台灣姓名
  name: (): string => {
    const lastName = faker.helpers.arrayElement(taiwanData.lastNames) as string;
    const firstName = faker.helpers.arrayElement(taiwanData.firstNames) as string;
    return `${lastName}${firstName}`;
  },

  // 生成部門
  department: (): string => faker.helpers.arrayElement(taiwanData.departments) as string,

  // 生成職位（根據部門）
  position: (department: string): string => {
    const positions = taiwanData.positions[department] || ['專員'];
    return faker.helpers.arrayElement(positions) as string;
  },

  // 生成台北地址
  address: (): string => {
    const area = faker.helpers.arrayElement(taiwanData.taipeiAreas) as string;
    const street = faker.location.street();
    const number = faker.number.int({ min: 1, max: 999 });
    return `台北市${area}${street}${number}號`;
  },

  // 生成門店名稱
  storeName: (): string => {
    const area = faker.helpers.arrayElement(taiwanData.taipeiAreas) as string;
    const storeType = faker.helpers.arrayElement(taiwanData.storeTypes) as string;
    return `${area}${storeType}`;
  },
};

export { faker };
