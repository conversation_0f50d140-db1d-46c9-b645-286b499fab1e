import { http, HttpResponse } from 'msw';

import { createPaginatedResponse, createResponse, delay } from '@/mocks/utils';

import { mockEmployees } from '../data';
import { Employee, EmployeeCreateRequest, EmployeeUpdateRequest } from '../types';

const employees = [...mockEmployees];

export const handlers = [
  // 獲取員工列表
  http.get('/api/employees', async ({ request }) => {
    await delay();

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const department = url.searchParams.get('department');
    const isActive = url.searchParams.get('isActive');

    let filteredEmployees = employees;

    if (department) {
      filteredEmployees = filteredEmployees.filter(emp =>
        emp.department.toLowerCase().includes(department.toLowerCase()),
      );
    }

    if (isActive !== null) {
      filteredEmployees = filteredEmployees.filter(emp => emp.isActive === (isActive === 'true'));
    }

    return HttpResponse.json(createPaginatedResponse(filteredEmployees, page, limit));
  }),

  // 獲取單一員工
  http.get('/api/employees/:id', async ({ params }) => {
    await delay();

    const { id } = params;
    const employee = employees.find(emp => emp.id === id);

    if (!employee) {
      return HttpResponse.json(
        { success: false, message: '員工不存在', timestamp: new Date().toISOString() },
        { status: 404 },
      );
    }

    return HttpResponse.json(createResponse(employee));
  }),

  // 創建員工
  http.post('/api/employees', async ({ request }) => {
    await delay();

    const body = (await request.json()) as EmployeeCreateRequest;

    // 驗證必填欄位
    if (!body.name || !body.department || !body.position) {
      return HttpResponse.json(
        { success: false, message: '缺少必填欄位', timestamp: new Date().toISOString() },
        { status: 400 },
      );
    }

    const newEmployee: Employee = {
      id: String(Date.now()),
      name: body.name,
      department: body.department,
      position: body.position,
      color: body.color || '#4F46E5',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    employees.push(newEmployee);

    return HttpResponse.json(createResponse(newEmployee, '員工創建成功'), { status: 201 });
  }),

  // 更新員工
  http.put('/api/employees/:id', async ({ params, request }) => {
    await delay();

    const { id } = params;
    const body = (await request.json()) as EmployeeUpdateRequest;

    const employeeIndex = employees.findIndex(emp => emp.id === id);

    if (employeeIndex === -1) {
      return HttpResponse.json(
        { success: false, message: '員工不存在', timestamp: new Date().toISOString() },
        { status: 404 },
      );
    }

    employees[employeeIndex] = {
      ...employees[employeeIndex],
      ...body,
      updatedAt: new Date().toISOString(),
    };

    return HttpResponse.json(createResponse(employees[employeeIndex], '員工更新成功'));
  }),

  // 刪除員工
  http.delete('/api/employees/:id', async ({ params }) => {
    await delay();

    const { id } = params;
    const employeeIndex = employees.findIndex(emp => emp.id === id);

    if (employeeIndex === -1) {
      return HttpResponse.json(
        { success: false, message: '員工不存在', timestamp: new Date().toISOString() },
        { status: 404 },
      );
    }

    employees.splice(employeeIndex, 1);

    return HttpResponse.json(createResponse(null, '員工刪除成功'));
  }),
];
