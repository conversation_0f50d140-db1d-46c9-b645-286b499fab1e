import { http, HttpResponse } from 'msw';

import { createResponse, delay } from '@/mocks/utils';

import { mockEmployees, mockShifts, mockStores } from '../data';

const employees = [...mockEmployees];
const stores = [...mockStores];
const shifts = [...mockShifts];

export const handlers = [
  // 獲取儀表板統計
  http.get('/api/dashboard/stats', async () => {
    await delay();

    const stats = {
      totalEmployees: employees.filter(emp => emp.isActive).length,
      totalStores: stores.filter(store => store.isActive).length,
      totalShifts: shifts.length,
      todayShifts: shifts.filter(shift => shift.date === new Date().toISOString().split('T')[0])
        .length,
    };

    return HttpResponse.json(createResponse(stats));
  }),
];
