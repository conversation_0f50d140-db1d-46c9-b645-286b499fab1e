import { http, HttpResponse } from 'msw';

import { createPaginatedResponse, createResponse, delay } from '@/mocks/utils';

import { mockEmployees, mockShifts, mockShiftTypes, mockStores } from '../data';
import { Shift, ShiftCreateRequest, ShiftUpdateRequest } from '../types';

const employees = [...mockEmployees];
const stores = [...mockStores];
const shifts = [...mockShifts];
const shiftTypes = [...mockShiftTypes];

export const handlers = [
  // 獲取排班列表
  http.get('/api/shifts', async ({ request }) => {
    await delay();

    const url = new URL(request.url);
    const employeeId = url.searchParams.get('employeeId');
    const storeId = url.searchParams.get('storeId');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const status = url.searchParams.get('status') as Shift['status'];
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');

    let filteredShifts = shifts;

    if (employeeId) {
      filteredShifts = filteredShifts.filter(shift => shift.employeeId === employeeId);
    }

    if (storeId) {
      filteredShifts = filteredShifts.filter(shift => shift.storeId === storeId);
    }

    if (startDate) {
      filteredShifts = filteredShifts.filter(shift => shift.date >= startDate);
    }

    if (endDate) {
      filteredShifts = filteredShifts.filter(shift => shift.date <= endDate);
    }

    if (status) {
      filteredShifts = filteredShifts.filter(shift => shift.status === status);
    }

    return HttpResponse.json(createPaginatedResponse(filteredShifts, page, limit));
  }),

  // 獲取單一排班
  http.get('/api/shifts/:id', async ({ params }) => {
    await delay();

    const { id } = params;
    const shift = shifts.find(s => s.id === id);

    if (!shift) {
      return HttpResponse.json(
        { success: false, message: '排班不存在', timestamp: new Date().toISOString() },
        { status: 404 },
      );
    }

    return HttpResponse.json(createResponse(shift));
  }),

  // 創建排班
  http.post('/api/shifts', async ({ request }) => {
    await delay();

    const body = (await request.json()) as ShiftCreateRequest;

    // 驗證必填欄位
    if (!body.employeeId || !body.storeId || !body.shiftTypeId || !body.date) {
      return HttpResponse.json(
        { success: false, message: '缺少必填欄位', timestamp: new Date().toISOString() },
        { status: 400 },
      );
    }

    // 驗證員工存在
    const employee = employees.find(emp => emp.id === body.employeeId);
    if (!employee) {
      return HttpResponse.json(
        { success: false, message: '員工不存在', timestamp: new Date().toISOString() },
        { status: 400 },
      );
    }

    // 驗證門店存在
    const store = stores.find(s => s.id === body.storeId);
    if (!store) {
      return HttpResponse.json(
        { success: false, message: '門店不存在', timestamp: new Date().toISOString() },
        { status: 400 },
      );
    }

    // 驗證班次類型存在
    const shiftType = shiftTypes.find(st => st.id === body.shiftTypeId);
    if (!shiftType) {
      return HttpResponse.json(
        { success: false, message: '班次類型不存在', timestamp: new Date().toISOString() },
        { status: 400 },
      );
    }

    // 計算班次時間
    const [startHour, startMinute] = shiftType.startTime.split(':').map(Number);
    const [endHour, endMinute] = shiftType.endTime.split(':').map(Number);

    const shiftDate = new Date(body.date);
    const startTime = new Date(shiftDate).setHours(startHour, startMinute, 0, 0);
    const endTime = new Date(shiftDate).setHours(endHour, endMinute, 0, 0);

    const newShift: Shift = {
      id: String(Date.now()),
      employeeId: body.employeeId,
      storeId: body.storeId,
      shiftTypeId: body.shiftTypeId,
      date: body.date,
      startTime,
      endTime,
      status: 'scheduled',
      notes: body.notes,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    shifts.push(newShift);

    return HttpResponse.json(createResponse(newShift, '排班創建成功'), { status: 201 });
  }),

  // 更新排班
  http.put('/api/shifts/:id', async ({ params, request }) => {
    await delay();

    const { id } = params;
    const body = (await request.json()) as ShiftUpdateRequest;

    const shiftIndex = shifts.findIndex(shift => shift.id === id);

    if (shiftIndex === -1) {
      return HttpResponse.json(
        { success: false, message: '排班不存在', timestamp: new Date().toISOString() },
        { status: 404 },
      );
    }

    shifts[shiftIndex] = {
      ...shifts[shiftIndex],
      ...body,
      updatedAt: new Date().toISOString(),
    };

    return HttpResponse.json(createResponse(shifts[shiftIndex], '排班更新成功'));
  }),

  // 刪除排班
  http.delete('/api/shifts/:id', async ({ params }) => {
    await delay();

    const { id } = params;
    const shiftIndex = shifts.findIndex(shift => shift.id === id);

    if (shiftIndex === -1) {
      return HttpResponse.json(
        { success: false, message: '排班不存在', timestamp: new Date().toISOString() },
        { status: 404 },
      );
    }

    shifts.splice(shiftIndex, 1);

    return HttpResponse.json(createResponse(null, '排班刪除成功'));
  }),
];
