import { http, HttpResponse } from 'msw';

import { createPaginatedResponse, createResponse, delay } from '@/mocks/utils';

import { mockStores } from '../data';

const stores = [...mockStores];

export const handlers = [
  // 獲取門店列表
  http.get('/api/stores', async ({ request }) => {
    await delay();

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const isActive = url.searchParams.get('isActive');

    let filteredStores = stores;

    if (isActive !== null) {
      filteredStores = filteredStores.filter(store => store.isActive === (isActive === 'true'));
    }

    return HttpResponse.json(createPaginatedResponse(filteredStores, page, limit));
  }),

  // 獲取單一門店
  http.get('/api/stores/:id', async ({ params }) => {
    await delay();

    const { id } = params;
    const store = stores.find(s => s.id === id);

    if (!store) {
      return HttpResponse.json(
        { success: false, message: '門店不存在', timestamp: new Date().toISOString() },
        { status: 404 },
      );
    }

    return HttpResponse.json(createResponse(store));
  }),
];
