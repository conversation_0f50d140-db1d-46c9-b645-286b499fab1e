import { ApiResponse, PaginatedResponse } from '@/mocks/types';

// 模擬 API 延遲
export const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// 創建響應格式
export const createResponse = <T>(data: T, message?: string): ApiResponse<T> => ({
  success: true,
  data,
  message,
  timestamp: new Date().toISOString(),
});

export const createPaginatedResponse = <T>(
  data: T[],
  page: number = 1,
  limit: number = 10,
): PaginatedResponse<T> => {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedData = data.slice(startIndex, endIndex);

  return {
    success: true,
    data: paginatedData,
    pagination: {
      page,
      limit,
      total: data.length,
      totalPages: Math.ceil(data.length / limit),
    },
    timestamp: new Date().toISOString(),
  };
};
