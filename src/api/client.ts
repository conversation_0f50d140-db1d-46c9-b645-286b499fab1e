import { AxiosRequestConfig } from 'axios';
import { ZodSchema } from 'zod';

import {
  ApiResponse as mockApiResponse,
  Employee,
  EmployeeCreateRequest,
  EmployeeUpdateRequest,
  PaginatedResponse,
  Shift,
  ShiftCreateRequest,
  ShiftQueryParams,
  ShiftType,
  ShiftUpdateRequest,
  Store,
} from '@/mocks/types';
import { ApiResponse } from '@/types/interface/api';

import { apiClient } from './axios-instance';

// 基礎請求函數 (保持原有接口，內部使用 axios)
async function apiRequest<T>(
  endpoint: string,
  options: Omit<AxiosRequestConfig, 'url' | 'baseURL'> = {},
): Promise<T> {
  const response = await apiClient.request<T>({
    url: endpoint,
    ...options,
  });
  return response as T; // 攔截器已經返回 response.data
}

// ===== 員工相關 API =====
// 封裝 api 請求 + zod 驗證
export async function apiRequestWithSchema<
  TResponse = unknown,
  TPayload = unknown,
  TParams = unknown,
>(
  config: AxiosRequestConfig,
  options?: {
    responseSchema?: ZodSchema<TResponse>;
    payloadSchema?: ZodSchema<TPayload>;
    paramsSchema?: ZodSchema<TParams>;
  },
): Promise<TResponse> {
  const { responseSchema, payloadSchema, paramsSchema } = options || {};

  if (paramsSchema && config.params) {
    config.params = paramsSchema.parse(config.params);
  }

  if (payloadSchema && config.data) {
    config.data = payloadSchema.parse(config.data);
  }

  const res = (await apiClient.request<ApiResponse<TResponse>>(
    config,
  )) as unknown as ApiResponse<TResponse>;
  const data = res.data;

  if (responseSchema) {
    return responseSchema.parse(data) as TResponse;
  }

  return data as TResponse;
}

export const employeeAPI = {
  // 獲取員工列表
  getEmployees: async (params?: {
    page?: number;
    limit?: number;
    department?: string;
    isActive?: boolean;
  }): Promise<PaginatedResponse<Employee>> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.department) searchParams.append('department', params.department);
    if (params?.isActive !== undefined) searchParams.append('isActive', params.isActive.toString());

    const query = searchParams.toString();
    return apiRequest(`/employees${query ? `?${query}` : ''}`);
  },

  // 獲取單一員工
  getEmployee: async (id: string): Promise<mockApiResponse<Employee>> => {
    return apiRequest(`/employees/${id}`);
  },

  // 創建員工
  createEmployee: async (data: EmployeeCreateRequest): Promise<mockApiResponse<Employee>> => {
    return apiRequest('/employees', {
      method: 'POST',
      data, // axios 使用 data 而不是 body
    });
  },

  // 更新員工
  updateEmployee: async (
    id: string,
    data: EmployeeUpdateRequest,
  ): Promise<mockApiResponse<Employee>> => {
    return apiRequest(`/employees/${id}`, {
      method: 'PUT',
      data,
    });
  },

  // 刪除員工
  deleteEmployee: async (id: string): Promise<mockApiResponse<null>> => {
    return apiRequest(`/employees/${id}`, {
      method: 'DELETE',
    });
  },
};

// ===== 門店相關 API =====

export const storeAPI = {
  // 獲取門店列表
  getStores: async (params?: {
    page?: number;
    limit?: number;
    isActive?: boolean;
  }): Promise<PaginatedResponse<Store>> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.isActive !== undefined) searchParams.append('isActive', params.isActive.toString());

    const query = searchParams.toString();
    return apiRequest(`/stores${query ? `?${query}` : ''}`);
  },

  // 獲取單一門店
  getStore: async (id: string): Promise<mockApiResponse<Store>> => {
    return apiRequest(`/stores/${id}`);
  },
};

// ===== 班次類型相關 API =====

export const shiftTypeAPI = {
  // 獲取班次類型列表
  getShiftTypes: async (): Promise<mockApiResponse<ShiftType[]>> => {
    return apiRequest('/shift-types');
  },
};

// ===== 排班相關 API =====

export const shiftAPI = {
  // 獲取排班列表
  getShifts: async (params?: ShiftQueryParams): Promise<PaginatedResponse<Shift>> => {
    const searchParams = new URLSearchParams();

    if (params?.employeeId) searchParams.append('employeeId', params.employeeId);
    if (params?.storeId) searchParams.append('storeId', params.storeId);
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);
    if (params?.status) searchParams.append('status', params.status);
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    const query = searchParams.toString();
    return apiRequest(`/shifts${query ? `?${query}` : ''}`);
  },

  // 獲取單一排班
  getShift: async (id: string): Promise<mockApiResponse<Shift>> => {
    return apiRequest(`/shifts/${id}`);
  },

  // 創建排班
  createShift: async (data: ShiftCreateRequest): Promise<mockApiResponse<Shift>> => {
    return apiRequest('/shifts', {
      method: 'POST',
      data,
    });
  },

  // 更新排班
  updateShift: async (id: string, data: ShiftUpdateRequest): Promise<mockApiResponse<Shift>> => {
    return apiRequest(`/shifts/${id}`, {
      method: 'PUT',
      data,
    });
  },

  // 刪除排班
  deleteShift: async (id: string): Promise<mockApiResponse<null>> => {
    return apiRequest(`/shifts/${id}`, {
      method: 'DELETE',
    });
  },
};

// ===== 統計資訊 API =====

export const dashboardAPI = {
  // 獲取儀表板統計
  getStats: async (): Promise<
    mockApiResponse<{
      totalEmployees: number;
      totalStores: number;
      totalShifts: number;
      todayShifts: number;
    }>
  > => {
    return apiRequest('/dashboard/stats');
  },
};

// 導出 axios 實例，供需要直接使用的場景
export { apiClient };

// 導出取消請求的工具函數 (使用現代 AbortController)
export const createAbortController = () => new AbortController();

// 批量請求工具函數
export const batchRequests = async <T extends readonly unknown[]>(
  requests: readonly [...{ [K in keyof T]: Promise<T[K]> }],
): Promise<T> => {
  return Promise.all(requests) as Promise<T>;
};

// 帶取消功能的請求工具
export const requestWithCancel = async <T>(
  endpoint: string,
  options: Omit<AxiosRequestConfig, 'url' | 'baseURL'> = {},
  signal?: AbortSignal,
): Promise<T> => {
  return apiRequest<T>(endpoint, {
    ...options,
    signal,
  });
};
