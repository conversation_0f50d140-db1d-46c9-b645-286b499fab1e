import {
  useMutation,
  UseMutationOptions,
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from '@tanstack/react-query';
import { AxiosError, AxiosRequestConfig } from 'axios';
import { z } from 'zod';

import { apiClient } from './client';

// 基本響應類型
interface ResponseData<T> {
  data: T;
  meta: {
    httpStatusCode: number;
  };
  success: boolean;
  message?: string;
  error?: {
    code: string;
    message: string;
    details: string;
  };
}

// Query 配置選項
export interface UseApiQueryOptions<TData, TResponse>
  extends Omit<
    UseQueryOptions<TResponse, AxiosError<ResponseData<TResponse>>>,
    'queryKey' | 'queryFn'
  > {
  // Zod schema for request validation
  requestSchema?: z.ZodSchema<TData>;
  // Zod schema for response validation
  responseSchema?: z.ZodSchema<TResponse>;
  // 查詢參數
  params?: TData;
  // 是否啟用查詢
  enabled?: boolean;
  // 自定義錯誤處理
  customErrorHandler?: (error: AxiosError<ResponseData<TResponse>>) => boolean;
}

// Mutation 配置選項
export interface UseApiMutationOptions<TData, TResponse>
  extends Omit<
    UseMutationOptions<TResponse, AxiosError<ResponseData<TResponse>>, TData>,
    'mutationFn'
  > {
  // Zod schema for request validation
  requestSchema?: z.ZodSchema<TData>;
  // Zod schema for response validation
  responseSchema?: z.ZodSchema<TResponse>;
  // 自定義錯誤處理
  customErrorHandler?: (error: AxiosError<ResponseData<TResponse>>) => boolean;
  // 成功後要失效的查詢鍵
  invalidateQueries?: string[][];
  // 成功後要重新獲取的查詢鍵
  refetchQueries?: string[][];
}

// 默認錯誤處理
// const defaultErrorHandler = (error: AxiosError<ResponseData<unknown>>): string => {
//   if (error.response) {
//     const { status, data } = error.response;

//     switch (status) {
//       case 400:
//         return data.error?.message || '請求參數錯誤';
//       case 401:
//         return '未授權，請重新登入';
//       case 403:
//         return '權限不足';
//       case 404:
//         return '請求的資源不存在';
//       case 500:
//         return '伺服器內部錯誤';
//       default:
//         return data.error?.message || data.message || '請求失敗';
//     }
//   }

//   if (error.request) {
//     return '網路連接失敗，請檢查網路狀態';
//   }

//   return error.message || '未知錯誤';
// };

// export function useApiQuery<TData, TResponse>(options: useApiQueryOptions<TData, TResponse>) {
//   return useQuery(options);
// }

// 創建 API 請求函數
const createApiRequest = async <TData, TResponse>(
  config: AxiosRequestConfig,
  requestData?: TData,
  requestSchema?: z.ZodSchema<TData>,
  responseSchema?: z.ZodSchema<TResponse>,
): Promise<TResponse> => {
  // 驗證請求數據
  if (requestSchema && requestData) {
    try {
      requestSchema.parse(requestData);
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        const errorMessage = validationError.errors.map(err => err.message).join(', ');
        throw new Error(`請求參數驗證失敗: ${errorMessage}`);
      }
      throw validationError;
    }
  }

  // 構建請求配置
  const requestConfig: AxiosRequestConfig = {
    ...config,
    data: requestData,
    params: config.method === 'GET' ? requestData : config.params,
  };

  // 發送請求
  const response = await apiClient<ResponseData<TResponse>>(requestConfig);

  // 檢查業務邏輯成功狀態
  if (!response.data.success) {
    throw new Error(response.data.message || '業務邏輯錯誤');
  }

  // 驗證響應數據
  let validatedData = response.data.data;
  console.log('🚀 ~ validatedData:', validatedData);
  if (responseSchema) {
    try {
      validatedData = responseSchema.parse(response.data.data);
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        const errorMessage = validationError.errors.map(err => err.message).join(', ');
        throw new Error(`響應數據驗證失敗: ${errorMessage}`);
      }
      throw validationError;
    }
  }

  return validatedData;
};

// 主要的 useApiQuery Hook
export function useApiQuery<TData = unknown, TResponse = unknown>(
  queryKey: string[],
  config: AxiosRequestConfig,
  options: UseApiQueryOptions<TData, TResponse> = {},
) {
  const { requestSchema, responseSchema, params, ...queryOptions } = options;

  return useQuery<TResponse, AxiosError<ResponseData<TResponse>>>({
    queryKey: params ? [...queryKey, params] : queryKey,
    queryFn: () => createApiRequest(config, params, requestSchema, responseSchema),
    ...queryOptions,
  });
}

// 主要的 useApiMutation Hook
export function useApiMutation<TData = unknown, TResponse = unknown>(
  config: AxiosRequestConfig,
  options: UseApiMutationOptions<TData, TResponse> = {},
) {
  const queryClient = useQueryClient();
  const {
    requestSchema,
    responseSchema,
    customErrorHandler,
    invalidateQueries,
    refetchQueries,
    ...mutationOptions
  } = options;

  return useMutation<TResponse, AxiosError<ResponseData<TResponse>>, TData>({
    mutationFn: (data: TData) => createApiRequest(config, data, requestSchema, responseSchema),
    ...mutationOptions,
    onSuccess: (data, variables, context) => {
      // 失效相關查詢
      if (invalidateQueries) {
        invalidateQueries.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey });
        });
      }

      // 重新獲取相關查詢
      if (refetchQueries) {
        refetchQueries.forEach(queryKey => {
          queryClient.refetchQueries({ queryKey });
        });
      }

      // 調用原始成功處理
      if (mutationOptions.onSuccess) {
        mutationOptions.onSuccess(data, variables, context);
      }
    },
    onError: (error, variables, context) => {
      // 首先嘗試自定義錯誤處理
      if (customErrorHandler && customErrorHandler(error)) {
        return;
      }

      // 調用原始錯誤處理
      if (mutationOptions.onError) {
        mutationOptions.onError(error, variables, context);
      }
    },
  });
}

// // 便捷的 HTTP 方法封裝
// export function useApiGet<TData = unknown, TResponse = unknown>(
//   queryKey: string[],
//   url: string,
//   options: Omit<UseApiQueryOptions<TData, TResponse>, 'requestSchema'> = {},
// ) {
//   return useApiQuery<TData, TResponse>(queryKey, { method: 'GET', url }, options);
// }
