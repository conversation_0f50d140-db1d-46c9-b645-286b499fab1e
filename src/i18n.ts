import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpBackend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';

import { LanguagesEnum } from './types/enums/common';

export const supportedLngs = [
  LanguagesEnum.US,
  LanguagesEnum.TW,
  // LanguagesEnum.CN,
  // LanguagesEnum.HK,
  // LanguagesEnum.VI,
];

export const fallbackLng = LanguagesEnum.TW;
export const LOCAL_STORAGE_NAME = 'i18nextLng';
const defaultNS = 'common';

export function changeLanguage(lng: LanguagesEnum) {
  i18n.changeLanguage(lng);
}

i18n
  .use(HttpBackend)

  // detect user language
  // learn more: https://github.com/i18next/i18next-browser-languageDetector
  .use(LanguageDetector)

  // Bind i18next to React
  .use(initReactI18next)

  // init i18next
  // for all options read: https://www.i18next.com/overview/configuration-options
  .init({
    // debug: true,
    supportedLngs,
    nonExplicitSupportedLngs: false,
    fallbackLng, // Default language
    defaultNS,
    ns: [defaultNS],
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
    detection: {
      // 自訂語言偵測順序
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
  });

export default i18n;
