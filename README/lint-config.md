## 1. 專案 lint 工具總覽

- [ESLint](https://eslint.org/docs/latest/use/getting-started)：主力靜態檢查工具，負責 TypeScript/JavaScript/React 程式碼品質。
- [Prettier](https://prettier.io/docs/en/install)：程式碼格式化工具，確保團隊統一的程式碼風格。
- [<PERSON><PERSON>](https://typicode.github.io/husky/)：Git hooks 工具，確保 commit 前自動執行檢查。
-  [lint-staged](https://www.npmjs.com/package/lint-staged)：只檢查/修正 staged 的檔案，加速 commit 前的 lint/format 流程。

## 2. 各工具與插件詳細說明

### ESLint
> 靜態分析 TypeScript/JavaScript/React 程式碼，找出潛在錯誤與不一致風格。

#### 已安裝插件：
- `@eslint/js、typescript-eslint`：支援 JS/TS。
- `eslint-plugin-react-hooks`、`eslint-plugin-react-refresh`：檢查 React 相關規範。
- [`eslint-plugin-jsx-a11y`](https://www.npmjs.com/package/eslint-plugin-jsx-a11y)：檢查 JSX 無障礙(a11y)問題。
- [`eslint-plugin-simple-import-sort`](https://www.npmjs.com/package/eslint-plugin-simple-import-sort)：import 排序。
- [`eslint-plugin-prettier`](https://github.com/prettier/eslint-plugin-prettier)：讓 Prettier 規則能以 ESLint rule 形式出錯。

#### 配置檔案：`eslint.config.js`

這是專案的主要 ESLint 設定檔，採用 ESM 格式（可用 import/export）的，flat config 集中管理所有 lint 規則與插件（ESLint v9+ 引入）。

- **用途**：
  - 統一管理 JS/TS/React 相關的 lint 規則
  - 可根據專案需求擴充/調整 plugins 與 rules
  - 支援 ignore、files、languageOptions 等進階設定

- **結構說明**：
  - `extends`：
    - `@eslint/js` 官方推薦規則
    - `typescript-eslint` 推薦規則
  - `plugins`：
    - `react-hooks`：檢查 React hooks 使用規範
    - `react-refresh`：配合 React Fast Refresh
    - `simple-import-sort`：import 排序
    - `jsx-a11y`：JSX 無障礙檢查
  - `rules`：
    - 套用 `react-hooks` 官方推薦規則
    - `react-refresh/only-export-components: warn`：僅允許匯出 component，避免副作用
    - `simple-import-sort/imports: error`、`simple-import-sort/exports: error`：import/export 必須排序
    - `jsx-a11y/alt-text: error`：JSX 圖片/元件需有 alt 屬性
  - `languageOptions`：
    - 設定 ECMAScript 2020、瀏覽器全域變數
  - `ignores`：忽略 dist 目錄
  - 最後 concat `eslint-plugin-prettier/recommended`，讓 Prettier 規則也能以 ESLint rule 形式出錯

- **目前主要規則**：
  - React hooks 必須正確使用（如 useEffect 依賴完整）
  - import/export 必須排序
  - JSX 圖片/元件需有 alt 屬性
  - Prettier 格式錯誤會直接報錯

如需調整規則，可直接修改 `eslint.config.js`，即時生效。

## Prettier
> 自動格式化程式碼，確保風格一致。

與 ESLint 整合：
- `eslint-config-prettier` 關閉 ESLint 會與 Prettier 衝突的規則
- `eslint-plugin-prettier` 讓 Prettier 規則能以 ESLint rule 形式出錯。

### 配置檔案：.prettierrc

```json
{
  "plugins": ["prettier-plugin-tailwindcss"],
  "arrowParens": "avoid",
  "bracketSpacing": true,
  "endOfLine": "lf",
  "htmlWhitespaceSensitivity": "css",
  "insertPragma": false,
  "jsxSingleQuote": false,
  "printWidth": 100,
  "proseWrap": "always",
  "quoteProps": "as-needed",
  "requirePragma": false,
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "all",
  "useTabs": false
}
```

#### 配置說明

- **plugins**:
  - `prettier-plugin-tailwindcss`: 針對 Tailwind CSS classes 進行排序。
- **arrowParens**: 只有在需要時才加括號（如單一參數時省略括號）。
- **bracketSpacing**: 物件大括號內保留空格（如 `{ foo: bar }`）。
- **endOfLine**: 強制使用 LF 換行符號。
- **htmlWhitespaceSensitivity**: HTML 空白敏感度設為 css（依照 CSS display 屬性決定）。
- **insertPragma**: 不在檔案開頭插入格式化標記。
- **jsxSingleQuote**: JSX 屬性使用雙引號。
- **printWidth**: 每行最大寬度 100 字元。
- **proseWrap**: markdown 文字自動換行。
- **quoteProps**: 只有必要時才給物件屬性加引號。
- **requirePragma**: 不要求檔案開頭有格式化標記才格式化。
- **semi**: 每行結尾加分號。
- **singleQuote**: 使用單引號。
- **tabWidth**: 縮排為 2 個空格。
- **trailingComma**: 多行物件/陣列最後一項也加逗號。
- **useTabs**: 不使用 tab，使用空格縮排。

### Husky
> Husky 是一個 Git hooks 工具，可以在 Git 提交或其他 Git 事件發生時執行腳本。我們可以利用它來自動化程式碼檢查或格式化等任務，確保在程式碼提交之前符合團隊的標準。

- 用途：在 commit 前自動執行 lint-staged，確保進入 repo 的程式碼都經過檢查/格式化。
- 設定方式：prepare script 會自動安裝 Husky hooks。

#### 目前 hooks 配置

##### `pre-commit`
目前唯一啟用的 hook。每次 git commit 前會：
1. 取得所有 staged 的 JS/TS 檔案
2. 執行 `pnpm exec eslint --fix` 自動修復 lint 問題
3. 修復失敗則阻止 commit，需修正後才能提交
4. 修復成功則繼續 commit
5. 腳本內有註解，未來可改用 lint-staged 處理更多檔案型別


`pre-commit` 腳本：
```sh
echo "🧪 pre-commit 開始執行" # 確認是否有正確安裝的腳本

# 方法 1. 不透過 lint-staged 來執行
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|jsx|ts|tsx)$' | xargs)

if [ -n "$STAGED_FILES" ]; then
  echo "🔍 正在自動修復 Lint 問題..."
  if ! pnpm exec eslint --fix $STAGED_FILES; then
    echo "❌ Lint 修復失敗，請修正後再提交！"
    exit 1
  fi
  git add $STAGED_FILES
fi

# 方法 2. 透過 lint-staged 來執行（目前註解掉）
# pnpm lint-staged --max-warnings=0 --concurrent --retry=2

echo "✅ Lint 通過，準備提交！"
```

##### 未來擴充
目前尚未啟用 `commit-msg`，但 .husky 目錄已預留 commit-msg 腳本，可隨時新增（如驗證 commit message 格式）。

##### 執行方式
Husky 會在安裝依賴或執行 `pnpm run prepare` 時自動安裝 hooks，無需手動設定。

### lint-staged
- 主要功能：只對 staged 檔案執行 lint/format，加速 commit 前檢查。
- 用途：commit 前自動執行 ESLint/Prettier 修正。
- 設定（package.json）：
  - *.+(ts|tsx|jsx|js): eslint --cache --fix
  - *.+(ts|tsx|jsx|js|json|css|md|mdx|html): prettier --write
  - 不會 stash（lint-staged:config.stash: false）

## 3. 本機環境安裝與設定步驟

1. 安裝依賴（建議用 pnpm）：
   ```sh
   pnpm install
   ```
2. 初始化 Husky（如未自動執行）：
   ```sh
   pnpm run prepare
   ```
3. 建議安裝 VSCode 插件：
   - ESLint
   - Prettier
   - EditorConfig（如有 editorconfig）
4. 設定 VSCode settings.json（建議）：
   ```json
   {
     "editor.formatOnSave": true,
     "editor.codeActionsOnSave": {
       "source.fixAll.eslint": true
     }
   }
   ```

## 4. 開發流程建議與注意事項

- 開發時：隨時可手動執行
  ```sh
  pnpm run lint
  ```
- commit 前：Husky + lint-staged 會自動檢查/修正 staged 檔案，未通過無法 commit。
- 常見 lint 規則提醒：
  - import 排序錯誤會直接報錯
  - React hooks 規則嚴格檢查
  - JSX 圖片/元件需有 alt 屬性
  - Prettier 格式不符會直接修正

## 5. 參考資源與延伸閱讀

- [ESLint 官方](https://eslint.org/)
- [Prettier 官方](https://prettier.io/)
- [Husky 官方](https://typicode.github.io/husky/)
- [lint-staged 官方](https://github.com/okonet/lint-staged)
- [eslint-config-prettier](https://github.com/prettier/eslint-config-prettier)
- [prettier-plugin-tailwindcss](https://github.com/tailwindlabs/prettier-plugin-tailwindcss)
