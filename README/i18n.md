# i18n 多語系開發文件
> 此專案目前使用 `react-i18next` + `i18next` 處理多語系

## 1. 專案 i18n 架構

- 語系檔放在 `public/locales/{lang}/common.json`
  - 例如：`public/locales/en-US/common.json`、`public/locales/zh-TW/common.json`
- i18n 初始化在 `src/i18n.ts`
- 前端組件透過 i18n hook 或 function 取得翻譯字串

---

## 2. 新增/修改翻譯

1. **新增語言**
   - 複製一份現有語言資料夾（如 `en-US`），改成新語言代碼（如 `ja-JP`）。
   - 編輯 `common.json`，填入對應語言的翻譯。

2. **新增翻譯 key**
   - 在所有語言的 `common.json` 裡都加上新 key，確保同步。

3. **範例：common.json**
   ```json
   {
     "hello": "Hello",
     "welcome": "Welcome"
   }
   ```

---

## 3. 在 React 組件中使用 i18n

假設你用的是 `react-i18next`，常見寫法如下：

```tsx
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation();
  return <div>{t('hello')}</div>;
};
```

---

## 4. 切換語言

通常會有一個語言切換器，直接呼叫 i18n 的 `changeLanguage`：

```tsx
import i18n from '../i18n';

const switchToZh = () => i18n.changeLanguage('zh-TW');
const switchToEn = () => i18n.changeLanguage('en-US');
```

---

## 5. i18n 設定說明（`src/i18n.ts`）

現有 `src/i18n.ts` 設定重點如下：

```ts
import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpBackend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';

import { LanguagesEnum } from './types/enums/common';

export const supportedLngs = [
  LanguagesEnum.US,
  LanguagesEnum.TW,
];

const fallbackLng = LanguagesEnum.TW;
const defaultNS = 'common';
// const localStorageName = 'i18nextLng'

i18n
  .use(HttpBackend) // detect user language
  .use(LanguageDetector) // Bind i18next to React
  .use(initReactI18next)
  // init i18next
  .init({
    debug: false,
    supportedLngs,
    nonExplicitSupportedLngs: false,
    fallbackLng, // Default language
    defaultNS,
    ns: [defaultNS],
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
  });

export default i18n;
```
### 1. 套件與擴充
- `i18next`、`react-i18next`（React 綁定）
- `i18next-http-backend`（支援從 public/locales 載入 JSON）
- `i18next-browser-languagedetector`（自動偵測瀏覽器語言）。

### 2. 語言設定
- `supportedLngs` 來自 `LanguagesEnum`，目前只啟用 US（英文）和 TW（繁中），其他語言（CN, HK, VI）註解掉。
- `fallbackLng` 設為 TW，找不到語言時預設繁中。

### 3. 命名空間
- 只用 `common` 這個 namespace，對應 `/locales/{lang}/common.json`。

### 4. 語言檔載入
- 用 `HttpBackend`，`loadPath` 指向 `/locales/{{lng}}/{{ns}}.json`，即 public/locales 目錄。

### 5. 語言偵測
- 啟用 `LanguageDetector`，但偵測順序與快取（localStorage, navigator）目前註解掉，預設用套件內建行為。

### 6. 其他
- `debug` 預設關閉。
- `nonExplicitSupportedLngs: false`，只接受明確列在 supportedLngs 的語言。

#### 實際效果
- 使用者進站時會自動偵測語言（瀏覽器/系統），若不支援則 fallback 到繁中。
- 語言檔案會自動從 `/public/locales/{lang}/common.json` 載入。
- 只要在 `LanguagesEnum` 加語言、public/locales 補語言包即可擴充。

> 若要自訂語言偵測順序、localStorage 快取、或多 namespace，可直接調整 i18n.ts 設定。

---

## 6. 注意事項

- **key 命名要有語意**，避免重複。
- **所有語言檔案 key 要同步**，避免漏翻。
- **不要在程式碼硬寫字串**，都要用 `t('key')`。
- **多語系 UI 測試**：切換語言檢查排版、溢位。
