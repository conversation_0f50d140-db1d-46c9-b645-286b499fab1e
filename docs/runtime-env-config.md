# 運行時環境配置

## 概述

本專案支援在Docker容器運行時選擇對應的環境配置，無需重新構建鏡像即可適應不同的部署環境。所有環境配置都預先打包在鏡像中，確保配置的一致性和安全性。

## 工作原理

1. **構建階段**：將所有環境的配置文件 `public/configs/config.*.js` 打包到鏡像中
2. **運行階段**：容器啟動時，`docker-entrypoint.sh` 腳本會：
   - 讀取 `ENV` 環境變數（默認為 `tst`）
   - 驗證環境變數是否有效
   - 選擇對應的配置文件並複製為 `/usr/share/nginx/html/config.js`
3. **前端加載**：應用啟動時會加載 `config.js`，環境變數通過 `window.RUNTIME_CONFIG` 全局對象提供

## 支援的環境

本專案支援以下5個環境，每個環境都有對應的預配置文件：

| 環境 | 環境變數值 | 配置文件 | 說明 |
|------|-----------|----------|------|
| 開發環境 | `dev` | `config.dev.js` | 開發環境配置 |
| 測試環境 | `tst` | `config.tst.js` | 測試環境配置（默認） |
| 用戶驗收測試 | `uat` | `config.uat.js` | UAT環境配置 |
| 預生產環境 | `preprd` | `config.preprd.js` | 預生產環境配置 |
| 生產環境 | `prod` | `config.prod.js` | 生產環境配置 |

## 環境配置內容

每個環境配置文件包含以下配置項：

- `SERVER_ENV_DEVELOPMENT_ENV_VARIABLE` - 開發環境變數
- `SERVER_ENV` - 服務器環境標識
- `SERVER_ENV_WEB_SITE` - 前端網站地址
- `SERVER_ENV_AUTH_WEB_SITE` - 認證服務地址
- `SERVER_ENV_BACKEND_SERVER` - 主後端服務地址
- `SERVER_ENV_FD_BACKEND_SERVER` - FD後端服務地址
- `SERVER_ENV_PT_BACKEND_SERVER` - PT後端服務地址
- `SERVER_ENV_PT_REPORT_BACKEND_SERVER` - PT報表服務地址
- `SERVER_ENV_PY_BACKEND_SERVER` - Python後端服務地址

## 使用方式

### Docker Run

```bash
# 使用測試環境（默認）
docker run -p 8080:8080 mayo-pt-web

# 使用生產環境
docker run -p 8080:8080 -e ENV=prod mayo-pt-web

# 使用開發環境
docker run -p 8080:8080 -e ENV=dev mayo-pt-web
```

### Docker Compose

```yaml
version: '3.8'
services:
  # 測試環境
  web-tst:
    image: mayo-pt-web
    ports:
      - "8080:8080"
    environment:
      - ENV=tst

  # 生產環境
  web-prod:
    image: mayo-pt-web
    ports:
      - "8081:8080"
    environment:
      - ENV=prod

  # UAT環境
  web-uat:
    image: mayo-pt-web
    ports:
      - "8082:8080"
    environment:
      - ENV=uat
```

### Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mayo-pt-web-prod
spec:
  template:
    spec:
      containers:
      - name: web
        image: mayo-pt-web
        env:
        - name: ENV
          value: "prod"
        ports:
        - containerPort: 8080
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mayo-pt-web-tst
spec:
  template:
    spec:
      containers:
      - name: web
        image: mayo-pt-web
        env:
        - name: ENV
          value: "tst"
        ports:
        - containerPort: 8080
```

## 開發指南

### 添加新的環境變數

1. 在所有環境配置文件 `public/configs/config.*.js` 中添加新的配置項
2. 在 `src/types/global.d.ts` 中添加類型定義
3. 在 `src/utils/env-config.ts` 的 `ALL_KEYS` 數組中添加新的鍵名
4. 更新 `scripts/generate-configs.js` 中的環境配置模板

### 添加新環境

1. 在 `scripts/generate-configs.js` 中添加新環境的配置
2. 運行 `node scripts/generate-configs.js` 重新生成配置文件
3. 在 `build/docker-entrypoint.sh` 中的 case 語句中添加新環境
4. 更新文檔說明

### 批量更新配置文件

使用配置生成工具：

```bash
# 重新生成所有環境配置文件
node scripts/generate-configs.js
```

### 在代碼中使用環境變數

```typescript
import { getEnv } from '@/utils/env-config';

// 獲取後端API地址
const apiUrl = getEnv('VITE_SERVER_ENV_BACKEND_SERVER');

// 獲取當前環境
const env = getEnv('VITE_SERVER_ENV');
```

## 注意事項

1. **預配置安全**：所有環境配置都預先定義，避免運行時配置錯誤
2. **類型安全**：所有環境變數都有TypeScript類型檢查
3. **默認環境**：如果未指定 ENV 或指定無效值，會使用 tst 環境
4. **向後兼容**：如果運行時配置不存在，會自動回退到編譯時的環境變數
5. **配置版本控制**：所有配置文件都納入版本控制，便於追蹤變更

## 故障排除

### 檢查當前使用的配置

```bash
# 進入容器
docker exec -it <container_id> sh

# 查看當前使用的配置文件
cat /usr/share/nginx/html/config.js

# 查看所有可用的配置文件
ls -la /usr/share/nginx/html/configs/

# 檢查環境變數
echo $ENV
```

### 常見問題

1. **配置文件未找到**：檢查 ENV 環境變數是否設置正確
2. **使用了錯誤的環境**：確認 ENV 值是否為支援的環境之一
3. **應用無法啟動**：檢查 Nginx 配置和端口映射是否正確
4. **配置未生效**：確認 config.js 文件是否正確載入

### 支援的環境值

只支援以下環境值：`dev`, `tst`, `uat`, `preprd`, `prod`

如果設置了無效的環境值，系統會自動回退到 `tst` 環境。
