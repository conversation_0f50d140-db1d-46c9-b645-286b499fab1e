# MAYO-PT-Web 錯誤處理系統指南

## 📋 概覽

本專案實現了完整的應用程式錯誤狀態處理系統，提供統一的錯誤處理機制和用戶友善的錯誤頁面。

## 🎯 支援的錯誤類型

### HTTP 狀態碼錯誤
| 狀態碼 | 錯誤類型 | 描述 | 是否可重試 |
|--------|----------|------|------------|
| 400 | 請求參數錯誤 | 客戶端請求格式錯誤 | ❌ |
| 401 | 未授權訪問 | 需要重新登入 | ❌ |
| 403 | 權限不足 | 沒有訪問權限 | ❌ |
| 404 | 頁面未找到 | 資源不存在 | ❌ |
| 408 | 請求超時 | 請求處理時間過長 | ✅ |
| 429 | 請求過多 | 觸發限流機制 | ✅ |
| 500 | 服務器內部錯誤 | 服務器處理異常 | ✅ |
| 502 | 網關錯誤 | 上游服務器錯誤 | ✅ |
| 503 | 服務不可用 | 服務暫時維護 | ✅ |
| 504 | 網關超時 | 上游服務器超時 | ✅ |

### 應用程式錯誤
- **網路連接失敗** - 無法連接到服務器
- **請求超時** - 網路請求超時
- **JavaScript 運行時錯誤** - 代碼執行異常
- **代碼分割載入失敗** - Chunk load error
- **路由錯誤** - 頁面載入失敗

## 🏗️ 系統架構

### 錯誤處理層級
```
1. 全局錯誤邊界 (ErrorBoundary)
   ├── 2. 路由錯誤處理 (RouteErrorBoundary)
   ├── 3. HTTP 攔截器 (Axios interceptors)
   └── 4. 組件級錯誤處理 (useErrorHandler)
```

### 核心組件
- **ErrorPage** - 統一錯誤頁面組件
- **ErrorBoundary** - React 錯誤邊界
- **RouteErrorBoundary** - 路由錯誤邊界
- **ErrorNotifications** - 錯誤通知組件

## 🚀 使用方式

### 1. 使用錯誤處理 Hook

```typescript
import { useErrorHandler } from '@/hooks/useErrorHandler';

function MyComponent() {
  const {
    handleApiError,
    handleNetworkError,
    showErrorNotification,
    clearError
  } = useErrorHandler();

  const handleSubmit = async () => {
    try {
      await apiCall();
    } catch (error) {
      handleApiError(error, () => handleSubmit()); // 提供重試函數
    }
  };

  return (
    <button onClick={handleSubmit}>
      提交
    </button>
  );
}
```

### 2. 手動顯示錯誤頁面

```typescript
import { ErrorPage } from '@/components/error';

function CustomErrorPage() {
  return (
    <ErrorPage
      type="server"
      errorCode={500}
      title="服務器錯誤"
      description="服務器暫時無法處理您的請求"
      showRetry={true}
      onRetry={() => window.location.reload()}
    />
  );
}
```

### 3. 錯誤狀態管理

```typescript
import { useAtom } from 'jotai';
import { 
  globalErrorAtom, 
  setGlobalErrorAtom,
  clearGlobalErrorAtom 
} from '@/lib/jotai/error-atoms';

function ErrorStatus() {
  const [error] = useAtom(globalErrorAtom);
  const [, setError] = useAtom(setGlobalErrorAtom);
  const [, clearError] = useAtom(clearGlobalErrorAtom);

  if (error) {
    return <div>錯誤: {error.message}</div>;
  }

  return <div>一切正常</div>;
}
```

### 4. 顯示通知

```typescript
const { 
  showSuccessNotification,
  showWarningNotification,
  showErrorNotification 
} = useErrorHandler();

// 成功通知
showSuccessNotification('成功', '操作已完成');

// 警告通知
showWarningNotification('警告', '請注意此操作');

// 錯誤通知（帶操作按鈕）
showErrorNotification('錯誤', '操作失敗', [
  {
    label: '重試',
    action: () => retryOperation(),
    variant: 'primary'
  },
  {
    label: '取消',
    action: () => cancelOperation(),
    variant: 'secondary'
  }
]);
```

## ⚙️ 配置選項

### 重試配置
```typescript
// src/api/axios-instance.ts
const RETRY_CONFIG = {
  maxRetries: 3,           // 最大重試次數
  retryDelay: 1000,        // 基礎延遲時間（毫秒）
  retryableStatuses: [408, 429, 500, 502, 503, 504], // 可重試的狀態碼
  retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', 'ECONNABORTED'], // 可重試的錯誤類型
};
```

### 錯誤邊界配置
```typescript
<ErrorBoundary
  showErrorDetails={isDev}  // 開發環境顯示錯誤詳情
  onError={(error, errorInfo) => {
    // 自定義錯誤處理邏輯
    console.error('錯誤:', error, errorInfo);
  }}
>
  <App />
</ErrorBoundary>
```

## 🧪 測試

### 測試頁面
訪問 `/error-test` 頁面可以測試各種錯誤情境：
- API 錯誤測試
- 網路錯誤測試
- 運行時錯誤測試
- 代碼載入錯誤測試
- 路由錯誤測試
- 通知系統測試

### 手動測試方法
```typescript
// 測試 API 錯誤
fetch('/api/non-existent-endpoint');

// 測試 JavaScript 錯誤
throw new Error('測試錯誤');

// 測試 404 錯誤
window.location.href = '/non-existent-route';
```

## 📱 用戶體驗

### 錯誤頁面特色
- **統一設計** - 一致的視覺風格
- **友善訊息** - 易懂的錯誤說明
- **操作引導** - 明確的下一步指示
- **重試機制** - 便捷的重試按鈕
- **返回首頁** - 快速回到主頁

### 通知系統特色
- **即時顯示** - 錯誤發生時立即提醒
- **自動消失** - 可配置自動關閉時間
- **操作按鈕** - 支援自定義操作
- **分類顯示** - 不同類型使用不同顏色

## 🔧 開發指南

### 添加新的錯誤類型
1. 在 `error-atoms.ts` 中定義錯誤類型
2. 在 `useErrorHandler.ts` 中添加處理方法
3. 在 `ErrorPage.tsx` 中添加顯示邏輯

### 自定義錯誤頁面
```typescript
import { ErrorPage } from '@/components/error';

function CustomErrorPage() {
  return (
    <ErrorPage
      type="generic"
      title="自定義錯誤"
      description="這是一個自定義錯誤頁面"
      icon={<CustomIcon />}
      actions={<CustomActions />}
    />
  );
}
```

### 錯誤監控整合
```typescript
// 在 ErrorBoundary 中添加錯誤報告
componentDidCatch(error: Error, errorInfo: ErrorInfo) {
  // 發送到錯誤監控服務
  if (import.meta.env.PROD) {
    sendToErrorService(error, errorInfo);
  }
}
```

## 📚 相關文件
- [API 客戶端配置](../src/api/axios-instance.ts)
- [錯誤狀態管理](../src/lib/jotai/error-atoms.ts)
- [錯誤處理 Hook](../src/hooks/useErrorHandler.ts)
- [錯誤組件](../src/components/error/)

## 🐛 常見問題

### Q: 如何禁用自動重試？
A: 在 API 請求中設置 `_retryCount` 為最大值或使用不可重試的錯誤類型。

### Q: 如何自定義錯誤訊息？
A: 使用 `showErrorNotification` 或直接設置 `globalErrorAtom`。

### Q: 如何處理特殊的業務錯誤？
A: 在 API 響應攔截器中添加特殊邏輯，或使用 `handleApiError` 的自定義處理。
