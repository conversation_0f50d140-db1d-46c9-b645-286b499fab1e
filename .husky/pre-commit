echo "🧪 pre-commit 開始執行" # 確認是否有正確安裝的腳本

# 方法 1. 不透過 lint-staged 來執行
# 取得已 staged 的 JS/TS 檔案
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|jsx|ts|tsx)$' | xargs)

# Lint & fix
if [ -n "$STAGED_FILES" ]; then
  echo "🔍 正在自動修復 Lint 問題..."
  if ! pnpm exec eslint --fix $STAGED_FILES; then
    echo "❌ Lint 修復失敗，請修正後再提交！"
    exit 1
  fi
  git add $STAGED_FILES
fi

# 方法 2. 透過 lint-staged 來執行
# pnpm lint-staged --max-warnings=0 --concurrent --retry=2

echo "✅ Lint 通過，準備提交！"