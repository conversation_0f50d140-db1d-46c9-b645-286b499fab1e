import react from '@vitejs/plugin-react-swc';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist-wc',
    lib: {
      entry: './src/components/web-components/index.ts',
      name: 'MayoPTWebComponents',
      fileName: format => `mayo-pt-wc.${format}.js`,
      formats: ['es', 'umd'],
    },
    rollupOptions: {
      output: {},
    },
  },
});
