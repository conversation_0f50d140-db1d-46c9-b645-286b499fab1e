// Axios 重構功能測試示例
// 此文件展示重構後的新功能和使用方法

import {
  apiClient,
  batchRequests,
  createAbortController,
  employeeAPI,
  requestWithCancel,
  shiftAPI,
  storeAPI,
} from './src/api/client';

// ===== 基本 API 調用測試 =====

async function testBasicAPI() {
  console.log('=== 基本 API 調用測試 ===');

  try {
    // 測試員工 API
    const employees = await employeeAPI.getEmployees({ page: 1, limit: 5 });
    console.log('員工列表:', employees);

    // 測試門店 API
    const stores = await storeAPI.getStores({ limit: 3 });
    console.log('門店列表:', stores);
  } catch (error) {
    console.error('基本 API 測試失敗:', error);
  }
}

// ===== 批量請求測試 =====

async function testBatchRequests() {
  console.log('=== 批量請求測試 ===');

  try {
    const [employees, stores, shifts] = await batchRequests([
      employeeAPI.getEmployees({ limit: 3 }),
      storeAPI.getStores({ limit: 3 }),
      shiftAPI.getShifts({ limit: 5 }),
    ]);

    console.log('批量請求結果:', { employees, stores, shifts });
  } catch (error) {
    console.error('批量請求測試失敗:', error);
  }
}

// ===== 請求取消測試 =====

async function testCancelRequest() {
  console.log('=== 請求取消測試 ===');

  const controller = createAbortController();

  // 2秒後取消請求
  setTimeout(() => {
    console.log('取消請求...');
    controller.abort();
  }, 2000);

  try {
    const employees = await requestWithCancel('/employees', { method: 'GET' }, controller.signal);
    console.log('請求成功:', employees);
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.log('請求已被取消');
    } else {
      console.error('請求取消測試失敗:', error);
    }
  }
}

// ===== 直接使用 axios 實例測試 =====

async function testDirectAxios() {
  console.log('=== 直接使用 axios 實例測試 ===');

  try {
    // 直接使用 apiClient 進行更複雜的請求
    const response = await apiClient.get('/employees', {
      params: { page: 1, limit: 2 },
      timeout: 5000,
    });

    console.log('直接 axios 請求結果:', response);
  } catch (error) {
    console.error('直接 axios 測試失敗:', error);
  }
}

// ===== 錯誤處理測試 =====

async function testErrorHandling() {
  console.log('=== 錯誤處理測試 ===');

  try {
    // 測試不存在的端點
    await employeeAPI.getEmployee('non-existent-id');
  } catch (error) {
    console.log('預期的錯誤處理:', error instanceof Error ? error.message : error);
  }
}

// ===== 並發請求測試 =====

async function testConcurrentRequests() {
  console.log('=== 並發請求測試 ===');

  const startTime = Date.now();

  try {
    // 同時發起多個請求
    await Promise.all([
      employeeAPI.getEmployees({ page: 1 }),
      employeeAPI.getEmployees({ page: 2 }),
      storeAPI.getStores(),
      shiftAPI.getShifts({ limit: 10 }),
    ]);

    const endTime = Date.now();
    console.log(`並發請求完成，耗時: ${endTime - startTime}ms`);
  } catch (error) {
    console.error('並發請求測試失敗:', error);
  }
}

// ===== 執行所有測試 =====

export async function runAllTests() {
  console.log('🚀 開始 axios 重構功能測試\n');

  await testBasicAPI();
  console.log('');

  await testBatchRequests();
  console.log('');

  await testCancelRequest();
  console.log('');

  await testDirectAxios();
  console.log('');

  await testErrorHandling();
  console.log('');

  await testConcurrentRequests();
  console.log('');

  console.log('✅ 所有測試完成');
}

// 使用方式：
// import { runAllTests } from './axios-test-example';
// runAllTests();

// ===== 實際使用示例 =====

export const usageExamples = {
  // 基本使用 (與之前完全相同)
  basic: async () => {
    const employees = await employeeAPI.getEmployees();
    return employees;
  },

  // 使用取消功能
  withCancel: async () => {
    const controller = createAbortController();
    // 在需要時調用 controller.abort() 取消請求
    return requestWithCancel('/employees', {}, controller.signal);
  },

  // 批量請求
  batch: async () => {
    return batchRequests([employeeAPI.getEmployees(), storeAPI.getStores()]);
  },

  // 直接使用 axios 實例
  advanced: async () => {
    return apiClient.get('/employees', {
      timeout: 3000,
      headers: { 'Custom-Header': 'value' },
    });
  },
};
