{"root": ["./src/app.tsx", "./src/i18n.ts", "./src/main.tsx", "./src/api/axios-instance.ts", "./src/api/client.ts", "./src/api/usequery.ts", "./src/components/shiftbadge/holiday.tsx", "./src/components/shiftbadge/index.tsx", "./src/components/auth/authguard.tsx", "./src/components/auth/redirectingpage.tsx", "./src/components/auth/unauthorizedpage.tsx", "./src/components/auth/index.ts", "./src/components/common/breadcrumbs/index.tsx", "./src/components/demo/demoform/index.tsx", "./src/components/layout/index.tsx", "./src/components/layout/sidebar/app-sidebar.tsx", "./src/components/layout/sidebar/index.tsx", "./src/components/layout/sidebar/logo.tsx", "./src/components/layout/sidebar/nav-main.tsx", "./src/components/layout/sidebar/site-header.tsx", "./src/components/layout/sidebar/user-menu/index.tsx", "./src/components/layout/sidebar/user-menu/language-dropdown.tsx", "./src/components/layout/sidebar/user-menu/linkup.tsx", "./src/components/layout/sidebar/user-menu/user-dropdown.tsx", "./src/components/schedule/types.ts", "./src/components/schedule/atoms/employeeatoms.ts", "./src/components/schedule/atoms/timelineatoms.ts", "./src/components/schedule/atoms/timelineitemsatom.ts", "./src/components/schedule/components/shiftscheduler.tsx", "./src/components/schedule/components/shiftschedulerv2.tsx", "./src/components/schedule/components/storefilter.tsx", "./src/components/schedule/components/timelinecomponents.tsx", "./src/components/schedule/components/timelinecontent.tsx", "./src/components/schedule/components/timelinecontrols.tsx", "./src/components/schedule/components/timelinedata.ts", "./src/components/schedule/components/timelinedemo.tsx", "./src/components/schedule/components/viewswitcher.tsx", "./src/components/schedule/constants/timelineconstants.ts", "./src/hooks/useembedmode.ts", "./src/hooks/api/usedashboard.ts", "./src/hooks/api/useemployees.ts", "./src/hooks/api/usegetbasedateoptions.ts", "./src/hooks/api/usegetscheduledepartments.ts", "./src/hooks/api/usegetshiftschedule.ts", "./src/hooks/api/useshifts.ts", "./src/hooks/api/usestores.ts", "./src/hooks/api/user/usegetuserinfo.ts", "./src/hooks/usedaterangefield/indext.ts", "./src/hooks/usedaterangenavigation/indext.tsx", "./src/hooks/useresponsivecontainer/index.ts", "./src/hooks/usesearchbarform/index.ts", "./src/lib/jotai/demo/index.tsx", "./src/lib/jotai/embedmode/index.tsx", "./src/lib/jotai/shiftscheduleapproval/index.tsx", "./src/lib/jotai/user/index.tsx", "./src/mocks/browser.ts", "./src/mocks/node.ts", "./src/mocks/types.ts", "./src/mocks/config/faker.ts", "./src/mocks/data/employees.ts", "./src/mocks/data/index.ts", "./src/mocks/data/shifts.ts", "./src/mocks/data/stores.ts", "./src/mocks/handlers/dashboard.ts", "./src/mocks/handlers/employees.ts", "./src/mocks/handlers/index.ts", "./src/mocks/handlers/shifts.ts", "./src/mocks/handlers/stores.ts", "./src/mocks/utils/index.ts", "./src/pages/basicdemopage.tsx", "./src/pages/demoformpage.tsx", "./src/pages/mswdemopage.tsx", "./src/pages/shiftschedulerpage.tsx", "./src/pages/timelinedemopage.tsx", "./src/pages/shiftscheduleapproval/index.tsx", "./src/pages/shiftscheduleapproval/components/daterangefield/index.tsx", "./src/pages/shiftscheduleapproval/components/daterangefield/flexiblehourdatepicker/index.tsx", "./src/pages/shiftscheduleapproval/components/daterangefield/navigationbutton/index.tsx", "./src/pages/shiftscheduleapproval/components/daterangefield/standardhourdatepicker/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulebuttongroup/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulebuttongroup/generatescheduledropdownmenu/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulebuttongroup/morebutton/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulecalendar/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulecalendar/calendarheader/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulecalendar/calendarheader/calendarheaderdaycell/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulecalendar/daycell/components.tsx", "./src/pages/shiftscheduleapproval/components/schedulecalendar/daycell/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulecalendar/departmentstats/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulecalendar/departmentstats/dailyattendance/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulecalendar/departmentstats/statsnumber/index.tsx", "./src/pages/shiftscheduleapproval/components/schedulecalendar/employeerow/index.tsx", "./src/pages/shiftscheduleapproval/components/searchbarfields/index.tsx", "./src/pages/shiftscheduleapproval/constants/index.ts", "./src/pages/shiftscheduleapproval/constants/shift.ts", "./src/pages/shiftscheduleapproval/constants/styles.ts", "./src/pages/shiftscheduleapproval/utils/index.tsx", "./src/pages/shiftscheduleapproval/utils/shift-business-logic.ts", "./src/providers/queryprovider.tsx", "./src/routes/demoformroute.tsx", "./src/routes/basicdemoroute.tsx", "./src/routes/index.tsx", "./src/routes/mswdemoroute.tsx", "./src/routes/shiftscheduleapproval.tsx", "./src/routes/shiftschedulerroute.tsx", "./src/routes/timelinedemoroute.tsx", "./src/types/global.d.ts", "./src/types/enums/common/cookie.tsx", "./src/types/enums/common/index.tsx", "./src/types/enums/shiftscheduleapproval/index.tsx", "./src/types/interface/api/index.tsx", "./src/types/interface/select/index.tsx", "./src/utils/auth.ts", "./src/utils/config.ts", "./src/utils/cookie.ts", "./src/utils/env-config.ts", "./src/utils/style.ts", "./src/utils/common/index.ts", "./vite.config.ts", "./vite.config.wc.ts", "./vite-env.d.ts", "./tailwind.config.ts"], "version": "5.7.3"}