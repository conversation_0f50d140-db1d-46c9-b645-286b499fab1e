# <類型>: <簡短描述>
# |
# | 類型應該是下列之一:
# |     feat     : 新功能
# |     fix      : 修復錯誤
# |     docs     : 文檔更改
# |     style    : 代碼格式變化，不影響代碼運行 (空格, 格式, 缺少分號等)
# |     refactor : 重構代碼，既不修復錯誤也不增加功能
# |     perf     : 性能優化
# |     test     : 添加或修改測試
# |     chore    : 構建過程或輔助工具和庫的變動，例如文檔生成
# |
# | 簡短描述應該簡明扼要 (不超過50個字符)
# |-----------------------------------------------------------

# 詳細說明，可選 (可使用多行)
# |
# |-----------------------------------------------------------

# 關聯的問題或工單，可選
# |
# | 解決: #123
# | 參考: #456
# |-----------------------------------------------------------

# 重大變更說明，可選
# |
# |----------------------------------------------------------- 