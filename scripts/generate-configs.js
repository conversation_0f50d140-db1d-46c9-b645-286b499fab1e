#!/usr/bin/env node

/**
 * 配置文件生成工具
 *
 * 用途：根據環境配置模板批量生成各環境的配置文件
 * 使用：node scripts/generate-configs.js
 */

const fs = require('fs');
const path = require('path');

// 環境配置模板
const environments = {
  dev: {
    SERVER_ENV_DEVELOPMENT_ENV_VARIABLE: 'dev',
    SERVER_ENV: 'dev',
    SERVER_ENV_WEB_SITE: 'https://dev-hrm-frontend.mayohr.com',
    SERVER_ENV_AUTH_WEB_SITE: 'https://dev-auth.mayohr.com/HRM',
    SERVER_ENV_BACKEND_SERVER: 'https://dev-apolloxe.mayohr.com/backend/',
    SERVER_ENV_FD_BACKEND_SERVER: 'https://dev-apolloxe.mayohr.com/backend/fd/api',
    SERVER_ENV_PT_BACKEND_SERVER: 'https://dev-apolloxe.mayohr.com/backend/pt/api',
    SERVER_ENV_PT_REPORT_BACKEND_SERVER: 'https://dev-pt-report-backend.mayohr.com/api',
    SERVER_ENV_PY_BACKEND_SERVER: 'https://dev-py-backend.mayohr.com/api',
  },
  tst: {
    SERVER_ENV_DEVELOPMENT_ENV_VARIABLE: 'tst',
    SERVER_ENV: 'tst',
    SERVER_ENV_WEB_SITE: 'https://tst-hrm-frontend.mayohr.com',
    SERVER_ENV_AUTH_WEB_SITE: 'https://tst-auth.mayohr.com/HRM',
    SERVER_ENV_BACKEND_SERVER: 'https://tst-apolloxe.mayohr.com/backend/',
    SERVER_ENV_FD_BACKEND_SERVER: 'https://tst-apolloxe.mayohr.com/backend/fd/api',
    SERVER_ENV_PT_BACKEND_SERVER: 'https://tst-apolloxe.mayohr.com/backend/pt/api',
    SERVER_ENV_PT_REPORT_BACKEND_SERVER: 'https://tst-pt-report-backend.mayohr.com/api',
    SERVER_ENV_PY_BACKEND_SERVER: 'https://tst-py-backend.mayohr.com/api',
  },
  uat: {
    SERVER_ENV_DEVELOPMENT_ENV_VARIABLE: 'uat',
    SERVER_ENV: 'uat',
    SERVER_ENV_WEB_SITE: 'https://uat-hrm-frontend.mayohr.com',
    SERVER_ENV_AUTH_WEB_SITE: 'https://uat-auth.mayohr.com/HRM',
    SERVER_ENV_BACKEND_SERVER: 'https://uat-apolloxe.mayohr.com/backend/',
    SERVER_ENV_FD_BACKEND_SERVER: 'https://uat-apolloxe.mayohr.com/backend/fd/api',
    SERVER_ENV_PT_BACKEND_SERVER: 'https://uat-apolloxe.mayohr.com/backend/pt/api',
    SERVER_ENV_PT_REPORT_BACKEND_SERVER: 'https://uat-pt-report-backend.mayohr.com/api',
    SERVER_ENV_PY_BACKEND_SERVER: 'https://uat-py-backend.mayohr.com/api',
  },
  preprd: {
    SERVER_ENV_DEVELOPMENT_ENV_VARIABLE: 'preprd',
    SERVER_ENV: 'preprd',
    SERVER_ENV_WEB_SITE: 'https://preprd-hrm-frontend.mayohr.com',
    SERVER_ENV_AUTH_WEB_SITE: 'https://preprd-auth.mayohr.com/HRM',
    SERVER_ENV_BACKEND_SERVER: 'https://preprd-apolloxe.mayohr.com/backend/',
    SERVER_ENV_FD_BACKEND_SERVER: 'https://preprd-apolloxe.mayohr.com/backend/fd/api',
    SERVER_ENV_PT_BACKEND_SERVER: 'https://preprd-apolloxe.mayohr.com/backend/pt/api',
    SERVER_ENV_PT_REPORT_BACKEND_SERVER: 'https://preprd-pt-report-backend.mayohr.com/api',
    SERVER_ENV_PY_BACKEND_SERVER: 'https://preprd-py-backend.mayohr.com/api',
  },
  prod: {
    SERVER_ENV_DEVELOPMENT_ENV_VARIABLE: 'prod',
    SERVER_ENV: 'prod',
    SERVER_ENV_WEB_SITE: 'https://hrm-frontend.mayohr.com',
    SERVER_ENV_AUTH_WEB_SITE: 'https://auth.mayohr.com/HRM',
    SERVER_ENV_BACKEND_SERVER: 'https://apolloxe.mayohr.com/backend/',
    SERVER_ENV_FD_BACKEND_SERVER: 'https://apolloxe.mayohr.com/backend/fd/api',
    SERVER_ENV_PT_BACKEND_SERVER: 'https://apolloxe.mayohr.com/backend/pt/api',
    SERVER_ENV_PT_REPORT_BACKEND_SERVER: 'https://pt-report-backend.mayohr.com/api',
    SERVER_ENV_PY_BACKEND_SERVER: 'https://py-backend.mayohr.com/api',
  },
};

// 確保目錄存在
const configsDir = path.join(__dirname, '../public/configs');
if (!fs.existsSync(configsDir)) {
  fs.mkdirSync(configsDir, { recursive: true });
}

// 生成配置文件
Object.entries(environments).forEach(([env, config]) => {
  const configContent = `window.RUNTIME_CONFIG = ${JSON.stringify(config, null, 2)};`;
  const filePath = path.join(configsDir, `config.${env}.js`);

  fs.writeFileSync(filePath, configContent, 'utf8');
  console.log(`✅ 已生成 ${env} 環境配置: ${filePath}`);
});

console.log('\n🎉 所有環境配置文件生成完成！');
console.log(`📁 配置目錄: ${configsDir}`);
console.log('📋 支援的環境: dev, tst, uat, preprd, prod');
