#!/bin/bash

# Docker 環境配置測試腳本
# 用途：快速測試不同環境的Docker容器配置

set -e

IMAGE_NAME="mayo-pt-web"
BASE_PORT=8080

echo "🚀 開始測試 Docker 環境配置..."

# 構建鏡像
echo "📦 構建 Docker 鏡像..."
docker build -f build/Dockerfile -t $IMAGE_NAME .

# 測試各個環境
environments=("dev" "tst" "uat" "preprd" "prod")

for env in "${environments[@]}"; do
    port=$((BASE_PORT + ${#env}))
    container_name="${IMAGE_NAME}-${env}"
    
    echo ""
    echo "🧪 測試 $env 環境 (端口: $port)..."
    
    # 停止並移除現有容器（如果存在）
    docker stop $container_name 2>/dev/null || true
    docker rm $container_name 2>/dev/null || true
    
    # 啟動容器
    docker run -d \
        --name $container_name \
        -p $port:8080 \
        -e ENV=$env \
        $IMAGE_NAME
    
    # 等待容器啟動
    sleep 3
    
    # 檢查容器狀態
    if docker ps | grep -q $container_name; then
        echo "✅ $env 環境容器啟動成功"
        echo "🌐 訪問地址: http://localhost:$port"
        
        # 檢查配置文件
        echo "📋 檢查配置文件..."
        docker exec $container_name cat /usr/share/nginx/html/config.js | head -3
    else
        echo "❌ $env 環境容器啟動失敗"
        docker logs $container_name
    fi
done

echo ""
echo "🎉 測試完成！"
echo ""
echo "📋 運行中的容器："
docker ps --filter "name=${IMAGE_NAME}" --format "table {{.Names}}\t{{.Ports}}\t{{.Status}}"

echo ""
echo "🧹 清理命令："
echo "docker stop \$(docker ps -q --filter \"name=${IMAGE_NAME}\")"
echo "docker rm \$(docker ps -aq --filter \"name=${IMAGE_NAME}\")"
